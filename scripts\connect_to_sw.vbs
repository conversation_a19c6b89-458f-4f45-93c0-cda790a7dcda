Option Explicit

' SolidWorks连接脚本
' 此脚本仅连接到SolidWorks并返回应用程序对象

Function ConnectToSolidWorks()
    ' 主要变量
    Dim swApp
    
    ' 尝试连接到SolidWorks
    On Error Resume Next
    WScript.Echo "正在连接到SolidWorks..."
    Set swApp = GetObject(, "SldWorks.Application")
    
    If Err.Number <> 0 Then
        Err.Clear
        WScript.Echo "SolidWorks未运行。尝试启动SolidWorks..."
        Set swApp = CreateObject("SldWorks.Application")
        
        If Err.Number <> 0 Then
            WScript.Echo "连接SolidWorks时出错: " & Err.Description
            WScript.Quit(1)
        End If
    End If
    
    ' 显示软件
    swApp.Visible = True
    WScript.Echo "成功连接到SolidWorks。"
    On Error Goto 0
    
    ' 返回应用程序对象
    Set ConnectToSolidWorks = swApp
End Function

' 如果直接运行此脚本则执行连接函数
If WScript.ScriptName = "connect_to_sw.vbs" Then
    Dim swApp
    Set swApp = ConnectToSolidWorks()
    WScript.Echo "SUCCESS: 连接成功"
    WScript.Quit(0)
End If 