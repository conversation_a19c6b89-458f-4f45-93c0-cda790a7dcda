Option Explicit

' SolidWorks材料设置工具脚本
' 提供快速材料设置功能
' 作者: SolidWorks AI助手

' 主要变量
Dim swApp, swModel, swModelDoc

' 连接到SolidWorks
On Error Resume Next
WScript.Echo "正在连接到SolidWorks..."
Set swApp = GetObject(, "SldWorks.Application")

If Err.Number <> 0 Then
    Err.Clear
    WScript.Echo "SolidWorks未运行，尝试启动..."
    Set swApp = CreateObject("SldWorks.Application")
    If Err.Number <> 0 Then
        WScript.Echo "连接SolidWorks时出错: " & Err.Description
        WScript.Quit(1)
    End If
End If

swApp.Visible = True
WScript.Echo "成功连接到SolidWorks。"
On Error Goto 0

' 获取当前活动文档
Set swModel = swApp.ActiveDoc
If swModel Is Nothing Then
    WScript.Echo "没有打开的文档。请先打开一个SolidWorks零件文档。"
    WScript.Quit(1)
End If

Set swModelDoc = swModel

' 检查文档类型
If swModelDoc.GetType <> 1 Then
    WScript.Echo "当前文档不是零件文档。材料只能应用于零件。"
    WScript.Quit(1)
End If

' 显示材料选择菜单
Dim choice
choice = InputBox("请选择材料:" & vbCrLf & _
                 "1 - 钢 (Steel)" & vbCrLf & _
                 "2 - 铝合金 (Aluminum)" & vbCrLf & _
                 "3 - 铜 (Copper)" & vbCrLf & _
                 "4 - 塑料 (Plastic)" & vbCrLf & _
                 "5 - 不锈钢 (Stainless Steel)" & vbCrLf & _
                 "6 - 铸铁 (Cast Iron)" & vbCrLf & _
                 "7 - 黄铜 (Brass)" & vbCrLf & _
                 "8 - 打开材料对话框", "材料设置工具", "1")

If choice = "" Then WScript.Quit(0)

Dim materialName, materialDatabase
materialDatabase = "solidworks materials"  ' 默认材料库

Select Case CInt(choice)
    Case 1
        ' 钢
        materialName = "AISI 1020"
        WScript.Echo "设置材料为: 钢 (AISI 1020)"
        
    Case 2
        ' 铝合金
        materialName = "1060 Alloy"
        WScript.Echo "设置材料为: 铝合金 (1060 Alloy)"
        
    Case 3
        ' 铜
        materialName = "Copper"
        WScript.Echo "设置材料为: 铜 (Copper)"
        
    Case 4
        ' 塑料
        materialName = "ABS PC"
        WScript.Echo "设置材料为: 塑料 (ABS PC)"
        
    Case 5
        ' 不锈钢
        materialName = "AISI 316"
        WScript.Echo "设置材料为: 不锈钢 (AISI 316)"
        
    Case 6
        ' 铸铁
        materialName = "Cast Carbon Steel"
        WScript.Echo "设置材料为: 铸铁 (Cast Carbon Steel)"
        
    Case 7
        ' 黄铜
        materialName = "Brass"
        WScript.Echo "设置材料为: 黄铜 (Brass)"
        
    Case 8
        ' 打开材料对话框
        WScript.Echo "打开材料设置对话框..."
        swApp.RunCommand 51701, ""  ' 材料属性对话框
        WScript.Echo "SUCCESS: 材料对话框已打开。"
        WScript.Quit(0)
        
    Case Else
        WScript.Echo "无效的选择。"
        WScript.Quit(1)
End Select

' 应用材料
On Error Resume Next
Dim boolResult
boolResult = swModelDoc.Extension.SetMaterialPropertyName2("", "", materialName)

If Err.Number <> 0 Then
    WScript.Echo "设置材料时出错: " & Err.Description
    Err.Clear
    
    ' 尝试使用不同的方法
    WScript.Echo "尝试使用材料对话框..."
    swApp.RunCommand 51701, ""  ' 材料属性对话框
    WScript.Echo "请在对话框中手动选择材料: " & materialName
Else
    If boolResult Then
        WScript.Echo "材料设置成功: " & materialName
        
        ' 获取材料属性信息
        Dim massProp
        Set massProp = swModelDoc.Extension.CreateMassProperty()
        
        If Not massProp Is Nothing Then
            Dim density
            density = massProp.Density
            WScript.Echo "材料密度: " & FormatNumber(density, 2) & " kg/m³"
        End If
        
        ' 刷新显示
        swModelDoc.GraphicsRedraw2
    Else
        WScript.Echo "材料设置失败，可能是材料名称不正确。"
        WScript.Echo "打开材料对话框进行手动设置..."
        swApp.RunCommand 51701, ""  ' 材料属性对话框
    End If
End If

On Error Goto 0

WScript.Echo "SUCCESS: 材料设置工具执行完成。"
WScript.Quit(0)
