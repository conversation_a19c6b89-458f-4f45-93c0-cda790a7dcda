Option Explicit

' 在SolidWorks中创建新文件的简单脚本
' 此脚本创建一个新的零件文件
' 作者: chaovy团队

' 主要变量
Dim swApp, swModel

' 尝试连接到SolidWorks
On Error Resume Next
WScript.Echo "正在连接到SolidWorks..."
Set swApp = GetObject(, "SldWorks.Application")

If Err.Number <> 0 Then
    Err.Clear
    WScript.Echo "SolidWorks未运行。尝试启动SolidWorks..."
    Set swApp = CreateObject("SldWorks.Application")
    
    If Err.Number <> 0 Then
        WScript.Echo "连接SolidWorks时出错: " & Err.Description
        WScript.Quit(1)
    End If
End If

' 显示软件
swApp.Visible = True
WScript.Echo "成功连接到SolidWorks。"
On Error Goto 0

' 创建新文档
On Error Resume Next
WScript.Echo "正在创建新文档..."

' 模板路径
Dim templatePath
templatePath = ""

' 检查常用路径以查找零件模板
If templatePath = "" Then
    ' 尝试使用用户设置
    templatePath = swApp.GetUserPreferenceStringValue(9)
End If

' 如果仍未找到，使用默认路径
If templatePath = "" Then
    templatePath = swApp.GetExecutablePath
    templatePath = Left(templatePath, InStrRev(templatePath, "\\"))
    templatePath = templatePath & "data\\templates\\Part.prtdot"
    
    If Err.Number <> 0 Then
        WScript.Echo "获取模板文件路径时出错: " & Err.Description
        ' 使用绝对路径作为最后解决方案
        templatePath = "C:\\ProgramData\\SOLIDWORKS\\SOLIDWORKS 2023\\templates\\Part.prtdot"
        Err.Clear
    End If
End If

WScript.Echo "使用模板: " & templatePath

' 创建新文档
Set swModel = swApp.NewDocument(templatePath, 0, 0, 0)

If Err.Number <> 0 Then
    WScript.Echo "创建新文档时出错: " & Err.Description
    WScript.Quit(1)
End If

If swModel Is Nothing Then
    WScript.Echo "错误: 模型未创建。"
    WScript.Quit(1)
End If

WScript.Echo "新文档创建成功。"

' 显示等轴测视图
swModel.ShowNamedView2 "*Isometric", 7
swModel.ViewZoomtofit2

WScript.Echo "SUCCESS: 新文件创建成功。"
WScript.Quit(0) 