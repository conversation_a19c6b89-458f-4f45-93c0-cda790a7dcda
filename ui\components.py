#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可复用UI组件
提供通用的UI控件和样式
"""

import tkinter as tk
from tkinter import scrolledtext
from typing import Optional, Callable, Any

from config import config_manager


class CustomButton(tk.Button):
    """自定义按钮控件"""
    
    def __init__(self, parent, text: str, command: Optional[Callable] = None, 
                 style: str = "custom", width: Optional[int] = None, 
                 height: Optional[int] = None, **kwargs):
        """
        Args:
            parent: 父窗口
            text: 按钮文字
            command: 点击回调函数
            style: 按钮样式 ("custom", "primary", "sidebar")
            width: 按钮宽度
            height: 按钮高度
        """
        self.ui_config = config_manager.config.ui
        self.style = style
        
        # 根据样式设置默认参数
        button_config = self._get_style_config(style)
        
        # 合并用户提供的参数
        button_config.update(kwargs)
        
        # 设置尺寸
        if width:
            button_config['width'] = width
        if height:
            button_config['height'] = height
            
        super().__init__(parent, text=text, command=self._handle_click(command), **button_config)
        
        # 绑定悬停效果
        self.bind("<Enter>", self._on_enter)
        self.bind("<Leave>", self._on_leave)
        self.bind("<Button-1>", self._on_press)
        self.bind("<ButtonRelease-1>", self._on_release)
        
        self.original_bg = button_config.get('bg', self.ui_config.button_bg)
        self.original_command = command
    
    def _get_style_config(self, style: str) -> dict:
        """获取样式配置"""
        base_config = {
            'font': self.ui_config.button_font,
            'bd': 0,
            'relief': tk.FLAT,
            'cursor': 'hand2',
            'padx': 10,
            'pady': 5
        }
        
        if style == "primary":
            base_config.update({
                'bg': "#4CAF50",  # 绿色主按钮
                'fg': "white",
                'activebackground': "#45A049",
                'activeforeground': "white"
            })
        elif style == "sidebar":
            base_config.update({
                'bg': self.ui_config.button_bg,  # 使用按钮背景色而不是侧边栏颜色
                'fg': "white",
                'activebackground': self.ui_config.accent_color,
                'activeforeground': "white",
                'justify': tk.LEFT,
                'anchor': tk.W
            })
        else:  # "custom" 默认样式
            base_config.update({
                'bg': self.ui_config.button_bg,
                'fg': "white",
                'activebackground': self.ui_config.button_active_bg,
                'activeforeground': "white"
            })
        
        return base_config
    
    def _handle_click(self, command: Optional[Callable]) -> Optional[Callable]:
        """处理点击事件，添加视觉反馈"""
        if not command:
            return None
            
        def wrapper(*args, **kwargs):
            # 点击效果
            try:
                return command(*args, **kwargs)
            except Exception as e:
                from logger import app_logger
                app_logger.log_error(e, f"按钮点击事件 {self.cget('text')}")
        
        return wrapper
    
    def _on_enter(self, event):
        """鼠标悬停"""
        if self.style == "primary":
            self.config(bg="#45A049")
        elif self.style == "sidebar":
            self.config(bg=self.ui_config.accent_color)
        else:
            self.config(bg=self.ui_config.button_active_bg)
    
    def _on_leave(self, event):
        """鼠标离开"""
        self.config(bg=self.original_bg)
    
    def _on_press(self, event):
        """鼠标按下"""
        if self.style == "primary":
            self.config(bg="#3E8E41")
        elif self.style == "sidebar":
            self.config(bg=self.ui_config.card_color)
        else:
            self.config(bg=self.ui_config.button_pressed_bg)
    
    def _on_release(self, event):
        """鼠标释放"""
        self._on_enter(event)  # 恢复悬停状态


class ScriptEditor(scrolledtext.ScrolledText):
    """脚本编辑器控件"""
    
    def __init__(self, parent, **kwargs):
        """
        Args:
            parent: 父窗口
            **kwargs: 其他参数
        """
        self.ui_config = config_manager.config.ui
        
        # 默认配置
        default_config = {
            'font': ("Consolas", 10),
            'background': self.ui_config.input_bg,
            'foreground': "white",
            'insertbackground': "white",
            'selectbackground': self.ui_config.accent_color,
            'selectforeground': "white",
            'wrap': tk.WORD,
            'borderwidth': 0,
            'relief': tk.FLAT,
            'padx': 10,
            'pady': 10
        }
        
        # 合并用户配置
        default_config.update(kwargs)
        
        super().__init__(parent, **default_config)
        
        # 设置语法高亮
        self._setup_syntax_highlighting()
        
        # 绑定事件
        self.bind('<KeyRelease>', self._on_text_change)
        self.bind('<Button-1>', self._on_text_change)
    
    def _setup_syntax_highlighting(self):
        """设置VBScript语法高亮"""
        try:
            # VBScript关键字
            keywords = [
                'Dim', 'Set', 'If', 'Then', 'Else', 'End', 'For', 'Next', 
                'While', 'Wend', 'Function', 'Sub', 'Call', 'Exit', 'Return',
                'True', 'False', 'Nothing', 'Empty', 'Null', 'Option', 'Explicit'
            ]
            
            # 配置标签样式
            self.tag_configure("keyword", foreground="#569CD6")  # 蓝色关键字
            self.tag_configure("string", foreground="#CE9178")   # 橙色字符串
            self.tag_configure("comment", foreground="#6A9955")  # 绿色注释
            self.tag_configure("number", foreground="#B5CEA8")   # 浅绿色数字
            
            self.keywords = keywords
            
        except Exception as e:
            from logger import app_logger
            app_logger.log_error(e, "设置语法高亮")
    
    def _on_text_change(self, event=None):
        """文本变化时更新语法高亮"""
        try:
            self._highlight_syntax()
        except Exception as e:
            from logger import app_logger
            app_logger.log_error(e, "语法高亮")
    
    def _highlight_syntax(self):
        """应用语法高亮"""
        try:
            # 清除现有标签
            for tag in ["keyword", "string", "comment", "number"]:
                self.tag_remove(tag, "1.0", tk.END)
            
            content = self.get("1.0", tk.END)
            lines = content.split('\n')
            
            for line_num, line in enumerate(lines, 1):
                line_start = f"{line_num}.0"
                
                # 高亮注释
                comment_pos = line.find("'")
                if comment_pos != -1:
                    comment_start = f"{line_num}.{comment_pos}"
                    comment_end = f"{line_num}.{len(line)}"
                    self.tag_add("comment", comment_start, comment_end)
                    line = line[:comment_pos]  # 不处理注释后的内容
                
                # 高亮字符串
                in_string = False
                string_start = None
                for i, char in enumerate(line):
                    if char == '"':
                        if not in_string:
                            string_start = f"{line_num}.{i}"
                            in_string = True
                        else:
                            string_end = f"{line_num}.{i+1}"
                            self.tag_add("string", string_start, string_end)
                            in_string = False
                
                # 高亮关键字
                import re
                for keyword in self.keywords:
                    pattern = r'\b' + re.escape(keyword) + r'\b'
                    for match in re.finditer(pattern, line, re.IGNORECASE):
                        start = f"{line_num}.{match.start()}"
                        end = f"{line_num}.{match.end()}"
                        self.tag_add("keyword", start, end)
                
                # 高亮数字
                number_pattern = r'\b\d+\.?\d*\b'
                for match in re.finditer(number_pattern, line):
                    start = f"{line_num}.{match.start()}"
                    end = f"{line_num}.{match.end()}"
                    self.tag_add("number", start, end)
                    
        except Exception as e:
            # 静默忽略语法高亮错误，不影响编辑器功能
            pass
    
    def set_content(self, content: str):
        """设置编辑器内容"""
        self.delete("1.0", tk.END)
        self.insert("1.0", content)
        self._highlight_syntax()
    
    def get_content(self) -> str:
        """获取编辑器内容"""
        return self.get("1.0", tk.END).rstrip('\n')


class StatusBar(tk.Frame):
    """状态栏控件"""
    
    def __init__(self, parent, **kwargs):
        """
        Args:
            parent: 父窗口
        """
        self.ui_config = config_manager.config.ui
        
        super().__init__(parent, 
                        bg=self.ui_config.sidebar_color,
                        relief=tk.SUNKEN,
                        bd=1,
                        **kwargs)
        
        # 状态标签
        self.status_label = tk.Label(
            self,
            text="就绪",
            bg=self.ui_config.sidebar_color,
            fg=self.ui_config.text_color,
            font=("Microsoft YaHei", 9),
            anchor=tk.W
        )
        self.status_label.pack(side=tk.LEFT, padx=10, pady=2)
        
        # 右侧信息标签
        self.info_label = tk.Label(
            self,
            text="",
            bg=self.ui_config.sidebar_color,
            fg=self.ui_config.secondary_text,
            font=("Microsoft YaHei", 8),
            anchor=tk.E
        )
        self.info_label.pack(side=tk.RIGHT, padx=10, pady=2)
    
    def set_status(self, text: str, level: str = "info"):
        """设置状态文本
        
        Args:
            text: 状态文本
            level: 级别 ("info", "success", "warning", "error")
        """
        color_map = {
            "info": self.ui_config.text_color,
            "success": "#4CAF50",
            "warning": "#FF9800",
            "error": "#F44336"
        }
        
        self.status_label.config(
            text=text,
            fg=color_map.get(level, self.ui_config.text_color)
        )
    
    def set_info(self, text: str):
        """设置右侧信息文本"""
        self.info_label.config(text=text)


class ProgressIndicator(tk.Frame):
    """进度指示器"""
    
    def __init__(self, parent, **kwargs):
        self.ui_config = config_manager.config.ui
        
        super().__init__(parent, bg=self.ui_config.bg_color, **kwargs)
        
        self.is_running = False
        self.current_step = 0
        
        # 创建进度点
        self.dots = []
        for i in range(3):
            dot = tk.Label(
                self,
                text="●",
                bg=self.ui_config.bg_color,
                fg=self.ui_config.secondary_text,
                font=("Arial", 12)
            )
            dot.pack(side=tk.LEFT, padx=2)
            self.dots.append(dot)
    
    def start(self):
        """开始动画"""
        self.is_running = True
        self._animate()
    
    def stop(self):
        """停止动画"""
        self.is_running = False
        for dot in self.dots:
            dot.config(fg=self.ui_config.secondary_text)
    
    def _animate(self):
        """执行动画"""
        if not self.is_running:
            return
        
        # 重置所有点
        for dot in self.dots:
            dot.config(fg=self.ui_config.secondary_text)
        
        # 高亮当前点
        if self.dots:
            self.dots[self.current_step].config(fg=self.ui_config.accent_color)
        
        # 移动到下一个点
        self.current_step = (self.current_step + 1) % len(self.dots)
        
        # 继续动画
        self.after(500, self._animate) 