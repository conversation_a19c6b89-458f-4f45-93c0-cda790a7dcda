#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对话框组件
包含各种对话框窗口
"""

import tkinter as tk
from tkinter import messagebox, scrolledtext, ttk
from typing import Optional, Callable
import os
import threading

from config import config_manager, MODEL_PROVIDERS
from logger import app_logger
from api_client import api_service
from .components import CustomButton, ScriptEditor, ProgressIndicator


class APISettingsDialog:
    """API设置对话框"""
    
    def __init__(self, parent, on_save_callback: Optional[Callable] = None):
        """
        Args:
            parent: 父窗口
            on_save_callback: 保存回调函数
        """
        self.parent = parent
        self.on_save_callback = on_save_callback
        self.config = config_manager.config
        self.ui_config = self.config.ui
        
        self.dialog = None
        self.api_key_var = tk.StringVar()
        self.base_url_var = tk.StringVar()
        self.model_var = tk.StringVar()
        self.provider_var = tk.StringVar()
        
        # UI控件引用
        self.provider_combo = None
        self.model_combo = None
        self.base_url_entry = None
        
        # 加载当前配置
        self._load_current_config()
    
    def _load_current_config(self):
        """加载当前API配置"""
        self.api_key_var.set(self.config.api.api_key)
        self.base_url_var.set(self.config.api.base_url)
        self.model_var.set(self.config.api.model)
        
        # 设置提供商，如果没有设置则默认为deepseek
        provider = getattr(self.config.api, 'provider', 'deepseek')
        self.provider_var.set(provider)
        
        # 根据provider设置提供商名称
        providers = config_manager.get_available_providers()
        provider_name = providers.get(provider, providers.get('deepseek', 'DeepSeek'))
        
        # 延迟设置下拉框的值，确保UI已创建
        def set_ui_values():
            if self.provider_combo:
                # 设置提供商选择
                try:
                    self.provider_combo.set(provider_name)
                    # 触发提供商变更事件以更新模型列表
                    self._on_provider_changed()
                except:
                    pass
        
        # 在UI创建后设置值
        if hasattr(self, 'dialog') and self.dialog:
            self.dialog.after(100, set_ui_values)
    
    def _load_ui_config(self):
        """在UI创建后加载配置"""
        try:
            # 根据provider设置提供商名称
            provider = getattr(self.config.api, 'provider', 'deepseek')
            providers = config_manager.get_available_providers()
            provider_name = providers.get(provider, providers.get('deepseek', 'DeepSeek'))
            
            # 设置提供商选择
            if self.provider_combo:
                self.provider_combo.set(provider_name)
                # 触发提供商变更事件以更新模型列表
                self._on_provider_changed()
                
        except Exception as e:
            app_logger.log_error(e, "加载UI配置")
    
    def show(self):
        """显示对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("API 设置")
        self.dialog.geometry("650x600")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        self.dialog.config(bg=self.ui_config.bg_color)
        
        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (650 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (600 // 2)
        self.dialog.geometry(f"650x600+{x}+{y}")
        
        self._create_widgets()
        
        # 在UI创建后加载配置
        self._load_ui_config()
    
    def _create_widgets(self):
        """创建界面控件"""
        # 标题
        title_label = tk.Label(
            self.dialog,
            text="🤖 AI API 设置",
            font=self.ui_config.header_font,
            bg=self.ui_config.bg_color,
            fg=self.ui_config.text_color
        )
        title_label.pack(pady=15)
        
        # 创建滚动区域
        self._create_scrollable_content()
        
        # 底部按钮框架（固定在底部）
        button_frame = tk.Frame(self.dialog, bg=self.ui_config.bg_color)
        button_frame.pack(fill=tk.X, padx=20, pady=15, side=tk.BOTTOM)
        
        # 测试连接按钮
        test_btn = CustomButton(
            button_frame,
            "🔗 测试连接",
            self._test_connection,
            style="custom"
        )
        test_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 保存按钮
        save_btn = CustomButton(
            button_frame,
            "💾 保存配置",
            self._save_settings,
            style="primary"
        )
        save_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 取消按钮
        cancel_btn = CustomButton(
            button_frame,
            "❌ 取消",
            self._cancel,
            style="custom"
        )
        cancel_btn.pack(side=tk.RIGHT)
    
    def _create_scrollable_content(self):
        """创建可滚动的内容区域"""
        # 创建画布和滚动条的容器
        scroll_container = tk.Frame(self.dialog, bg=self.ui_config.bg_color)
        scroll_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 10))
        
        # 创建画布
        canvas = tk.Canvas(
            scroll_container,
            bg=self.ui_config.bg_color,
            highlightthickness=0,
            bd=0
        )
        
        # 创建滚动条
        scrollbar = tk.Scrollbar(
            scroll_container,
            orient="vertical",
            command=canvas.yview,
            bg=self.ui_config.card_color,
            troughcolor=self.ui_config.bg_color,
            activebackground=self.ui_config.accent_color
        )
        
        # 创建内容框架
        scrollable_frame = tk.Frame(canvas, bg=self.ui_config.bg_color)
        
        # 配置滚动
        def _configure_scroll(event):
            canvas.configure(scrollregion=canvas.bbox("all"))
        
        def _configure_canvas(event):
            # 确保内容框架与画布宽度一致
            canvas_width = event.width
            canvas.itemconfig(canvas_window, width=canvas_width)
        
        scrollable_frame.bind("<Configure>", _configure_scroll)
        canvas.bind("<Configure>", _configure_canvas)
        
        # 将内容框架放入画布
        canvas_window = canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 布局画布和滚动条
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 绑定鼠标滚轮事件（跨平台兼容）
        def _on_mousewheel(event):
            # Windows 和 macOS
            if event.delta:
                canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            # Linux
            elif event.num == 4:
                canvas.yview_scroll(-1, "units")
            elif event.num == 5:
                canvas.yview_scroll(1, "units")
        
        # 绑定不同平台的滚轮事件
        canvas.bind("<MouseWheel>", _on_mousewheel)  # Windows 和 macOS
        canvas.bind("<Button-4>", _on_mousewheel)    # Linux
        canvas.bind("<Button-5>", _on_mousewheel)    # Linux
        
        # 确保焦点在画布上以响应滚轮事件
        def _on_canvas_focus(event):
            canvas.focus_set()
        
        canvas.bind("<Enter>", _on_canvas_focus)
        
        # 在可滚动框架中创建内容
        self._create_form_content(scrollable_frame)
        
        # 保存引用以便后续使用
        self.canvas = canvas
        self.scrollable_frame = scrollable_frame
    
    def _create_form_content(self, parent):
        """在可滚动区域中创建表单内容"""
        # AI模型提供商选择
        self._create_provider_selector(parent, row=0)
        
        # API Key
        self._create_field(parent, "🔑 API Key:", self.api_key_var, show="*", row=1)
        
        # Base URL (带自动填充)
        self._create_url_field(parent, row=2)
        
        # Model (下拉选择)
        self._create_model_selector(parent, row=3)
        
        # 配置说明区域
        self._create_info_section(parent, row=4)
    
    def _create_provider_selector(self, parent, row: int = 0):
        """创建AI模型提供商选择器"""
        # 标签
        label = tk.Label(
            parent,
            text="🏢 AI模型提供商:",
            font=self.ui_config.default_font,
            bg=self.ui_config.bg_color,
            fg=self.ui_config.text_color
        )
        label.grid(row=row, column=0, sticky="w", pady=10)
        
        # 提供商下拉框
        providers = config_manager.get_available_providers()
        self.provider_combo = ttk.Combobox(
            parent,
            textvariable=self.provider_var,
            values=list(providers.values()),
            font=self.ui_config.default_font,
            state="readonly",
            width=28
        )
        self.provider_combo.grid(row=row, column=1, sticky="ew", padx=(10, 0), pady=10)
        
        # 绑定选择事件
        self.provider_combo.bind("<<ComboboxSelected>>", self._on_provider_changed)
        
        # 配置列权重
        parent.grid_columnconfigure(1, weight=1)
    
    def _create_url_field(self, parent, row: int = 0):
        """创建Base URL字段（带自动填充）"""
        # 标签
        label = tk.Label(
            parent,
            text="🌐 Base URL:",
            font=self.ui_config.default_font,
            bg=self.ui_config.bg_color,
            fg=self.ui_config.text_color
        )
        label.grid(row=row, column=0, sticky="w", pady=10)
        
        # URL输入框
        self.base_url_entry = tk.Entry(
            parent,
            textvariable=self.base_url_var,
            font=self.ui_config.default_font,
            bg=self.ui_config.input_bg,
            fg="white",
            insertbackground="white",
            bd=0,
            relief=tk.FLAT,
            width=40
        )
        self.base_url_entry.grid(row=row, column=1, sticky="ew", padx=(10, 0), pady=10)
        
        # 配置列权重
        parent.grid_columnconfigure(1, weight=1)
    
    def _create_model_selector(self, parent, row: int = 0):
        """创建模型选择器"""
        # 标签
        label = tk.Label(
            parent,
            text="🤖 AI模型:",
            font=self.ui_config.default_font,
            bg=self.ui_config.bg_color,
            fg=self.ui_config.text_color
        )
        label.grid(row=row, column=0, sticky="w", pady=10)
        
        # 模型下拉框
        self.model_combo = ttk.Combobox(
            parent,
            textvariable=self.model_var,
            font=self.ui_config.default_font,
            state="readonly",
            width=28
        )
        self.model_combo.grid(row=row, column=1, sticky="ew", padx=(10, 0), pady=10)
        
        # 初始化模型列表
        self._update_model_list()
        
        # 配置列权重
        parent.grid_columnconfigure(1, weight=1)
    
    def _create_info_section(self, parent, row: int = 0):
        """创建信息说明区域"""
        # 基本说明
        basic_info_frame = tk.LabelFrame(
            parent,
            text="💡 配置说明",
            font=("Microsoft YaHei", 10, "bold"),
            bg=self.ui_config.card_color,
            fg=self.ui_config.text_color,
            padx=15,
            pady=10
        )
        basic_info_frame.grid(row=row, column=0, columnspan=2, sticky="ew", pady=(20, 10))
        
        basic_info_text = """🔑 API Key: 您的AI服务API密钥
🌐 Base URL: API服务地址（自动根据提供商填充）
🤖 AI模型: 选择要使用的AI模型"""
        
        basic_info_label = tk.Label(
            basic_info_frame,
            text=basic_info_text,
            font=("Microsoft YaHei", 9),
            bg=self.ui_config.card_color,
            fg=self.ui_config.secondary_text,
            justify=tk.LEFT
        )
        basic_info_label.pack(anchor=tk.W)
        
        # 支持的模型提供商
        providers_frame = tk.LabelFrame(
            parent,
            text="🌟 支持的AI模型提供商",
            font=("Microsoft YaHei", 10, "bold"),
            bg=self.ui_config.card_color,
            fg=self.ui_config.text_color,
            padx=15,
            pady=10
        )
        providers_frame.grid(row=row+1, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        
        # 分两列显示提供商
        providers_left = tk.Frame(providers_frame, bg=self.ui_config.card_color)
        providers_left.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        providers_right = tk.Frame(providers_frame, bg=self.ui_config.card_color)
        providers_right.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        left_providers = [
            "• DeepSeek - 性价比高，编程能力强",
            "• 豆包(字节) - 字节跳动大模型",
            "• 文心一言(百度) - 百度AI助手",
            "• 通义千问(阿里) - 阿里云AI服务"
        ]
        
        right_providers = [
            "• 智谱AI - 清华团队开发",
            "• Kimi - 月之暗面长文本模型",
            "• MiniMax - 国产大模型厂商",
            "• OpenAI - GPT系列模型"
        ]
        
        for provider in left_providers:
            tk.Label(
                providers_left,
                text=provider,
                font=("Microsoft YaHei", 8),
                bg=self.ui_config.card_color,
                fg=self.ui_config.secondary_text,
                justify=tk.LEFT
            ).pack(anchor=tk.W, pady=1)
        
        for provider in right_providers:
            tk.Label(
                providers_right,
                text=provider,
                font=("Microsoft YaHei", 8),
                bg=self.ui_config.card_color,
                fg=self.ui_config.secondary_text,
                justify=tk.LEFT
            ).pack(anchor=tk.W, pady=1)
        
        # 使用提示
        tips_frame = tk.LabelFrame(
            parent,
            text="💡 使用提示",
            font=("Microsoft YaHei", 10, "bold"),
            bg=self.ui_config.card_color,
            fg=self.ui_config.text_color,
            padx=15,
            pady=10
        )
        tips_frame.grid(row=row+2, column=0, columnspan=2, sticky="ew", pady=(0, 20))
        
        tips_text = """• 选择提供商后，Base URL和默认模型会自动填充
• 点击"测试连接"验证配置是否正确
• 配置保存后会立即生效，无需重启应用"""
        
        tips_label = tk.Label(
            tips_frame,
            text=tips_text,
            font=("Microsoft YaHei", 9),
            bg=self.ui_config.card_color,
            fg=self.ui_config.secondary_text,
            justify=tk.LEFT
        )
        tips_label.pack(anchor=tk.W)
    
    def _create_field(self, parent, label_text: str, var: tk.StringVar, 
                     show: Optional[str] = None, row: int = 0):
        """创建输入字段"""
        # 标签
        label = tk.Label(
            parent,
            text=label_text,
            font=self.ui_config.default_font,
            bg=self.ui_config.bg_color,
            fg=self.ui_config.text_color
        )
        label.grid(row=row, column=0, sticky="w", pady=10)
        
        # 输入框
        entry = tk.Entry(
            parent,
            textvariable=var,
            font=self.ui_config.default_font,
            bg=self.ui_config.input_bg,
            fg="white",
            insertbackground="white",
            bd=0,
            relief=tk.FLAT,
            width=30,
            show=show
        )
        entry.grid(row=row, column=1, sticky="ew", padx=(10, 0), pady=10)
        
        # 配置列权重
        parent.grid_columnconfigure(1, weight=1)
    
    def _on_provider_changed(self, event=None):
        """当提供商选择改变时的处理"""
        try:
            provider_name = self.provider_combo.get()
            # 查找对应的provider key
            providers = config_manager.get_available_providers()
            provider_key = None
            for key, name in providers.items():
                if name == provider_name:
                    provider_key = key
                    break
            
            if provider_key:
                provider_config = config_manager.get_provider_config(provider_key)
                
                # 自动填充Base URL
                if provider_key != "custom":
                    self.base_url_var.set(provider_config["base_url"])
                else:
                    self.base_url_var.set("")
                
                # 更新模型列表
                self._update_model_list(provider_key)
                
                # 设置默认模型
                if provider_config["models"]:
                    self.model_var.set(provider_config["default_model"])
                
        except Exception as e:
            app_logger.log_error(e, "提供商选择变更")
    
    def _update_model_list(self, provider_key: str = None):
        """更新模型列表"""
        try:
            if not provider_key:
                # 根据当前选择的提供商获取key
                provider_name = self.provider_combo.get() if self.provider_combo else ""
                providers = config_manager.get_available_providers()
                for key, name in providers.items():
                    if name == provider_name:
                        provider_key = key
                        break
                
                if not provider_key:
                    provider_key = self.provider_var.get() or "deepseek"
            
            provider_config = config_manager.get_provider_config(provider_key)
            models = provider_config["models"]
            
            if self.model_combo:
                self.model_combo['values'] = models
                
        except Exception as e:
            app_logger.log_error(e, "更新模型列表")
    
    def _test_connection(self):
        """测试API连接"""
        try:
            # 获取当前输入的配置
            api_key = self.api_key_var.get().strip()
            base_url = self.base_url_var.get().strip()
            model = self.model_var.get().strip()
            
            if not all([api_key, base_url, model]):
                messagebox.showerror("错误", "请填写所有必需的字段")
                return
            
            # 临时更新API配置进行测试
            temp_client = api_service.client
            temp_client.update_config(api_key, base_url, model)
            
            def test_thread():
                try:
                    success, message = api_service.test_api_connection()
                    
                    # 在主线程中显示结果
                    self.dialog.after(0, lambda: self._show_test_result(success, message))
                    
                except Exception as e:
                    self.dialog.after(0, lambda: self._show_test_result(False, str(e)))
            
            # 在后台线程中执行测试
            threading.Thread(target=test_thread, daemon=True).start()
            
            # 显示测试中状态
            messagebox.showinfo("测试连接", "正在测试连接，请稍候...")
            
        except Exception as e:
            app_logger.log_error(e, "测试API连接")
            messagebox.showerror("错误", f"测试连接时出错：{str(e)}")
    
    def _show_test_result(self, success: bool, message: str):
        """显示测试结果"""
        if success:
            messagebox.showinfo("连接成功", "API连接测试成功！")
        else:
            messagebox.showerror("连接失败", f"API连接测试失败：\n{message}")
    
    def _save_settings(self):
        """保存设置"""
        try:
            api_key = self.api_key_var.get().strip()
            base_url = self.base_url_var.get().strip()
            model = self.model_var.get().strip()
            
            # 获取选择的提供商
            provider_name = self.provider_combo.get()
            providers = config_manager.get_available_providers()
            provider_key = "deepseek"  # 默认值
            for key, name in providers.items():
                if name == provider_name:
                    provider_key = key
                    break
            
            if not all([api_key, base_url, model]):
                messagebox.showerror("错误", "请填写所有必需的字段")
                return
            
            # 保存配置
            config_manager.save_api_config(api_key, base_url, model, provider_key)
            
            # 更新API客户端
            api_service.client.update_config(api_key, base_url, model, provider_key)
            
            # 调用回调
            if self.on_save_callback:
                self.on_save_callback()
            
            app_logger.log_config_change("API设置", f"已保存 - 提供商: {provider_name}")
            messagebox.showinfo("成功", f"✅ API设置已保存！\n\n提供商: {provider_name}\n模型: {model}")
            
            self.dialog.destroy()
            
        except Exception as e:
            app_logger.log_error(e, "保存API设置")
            messagebox.showerror("错误", f"保存设置时出错：{str(e)}")
    
    def _cancel(self):
        """取消"""
        self.dialog.destroy()


class DonationDialog:
    """打赏对话框"""
    
    def __init__(self, parent):
        """
        Args:
            parent: 父窗口
        """
        self.parent = parent
        self.ui_config = config_manager.config.ui
        self.dialog = None
    
    def show(self):
        """显示对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("支持开发者")
        self.dialog.geometry("450x600")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        self.dialog.config(bg=self.ui_config.bg_color)
        
        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (450 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (600 // 2)
        self.dialog.geometry(f"450x600+{x}+{y}")
        
        self._create_widgets()
    
    def _create_widgets(self):
        """创建界面控件"""
        # 主框架
        main_frame = tk.Frame(self.dialog, bg=self.ui_config.bg_color, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = tk.Label(
            main_frame,
            text="❤️ 支持开发者",
            font=("Microsoft YaHei", 18, "bold"),
            bg=self.ui_config.bg_color,
            fg=self.ui_config.accent_color
        )
        title_label.pack(pady=(0, 20))
        
        # 感谢文字
        thanks_text = """感谢您使用 SolidWorks AI 助手！

如果这个工具对您有帮助，
您可以通过微信扫码打赏来支持开发者继续改进和维护。

您的支持是我们前进的动力！"""
        
        thanks_label = tk.Label(
            main_frame,
            text=thanks_text,
            font=("Microsoft YaHei", 11),
            bg=self.ui_config.bg_color,
            fg=self.ui_config.text_color,
            justify=tk.CENTER
        )
        thanks_label.pack(pady=(0, 30))
        
        # 微信支付区域
        payment_frame = tk.LabelFrame(
            main_frame,
            text="微信支付",
            font=("Microsoft YaHei", 12, "bold"),
            bg=self.ui_config.card_color,
            fg=self.ui_config.text_color,
            padx=20,
            pady=20
        )
        payment_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 收款人信息
        payee_label = tk.Label(
            payment_frame,
            text="收款人: bogy (*超)",
            font=("Microsoft YaHei", 10),
            bg=self.ui_config.card_color,
            fg=self.ui_config.text_color
        )
        payee_label.pack(pady=(0, 15))
        
        # 二维码区域
        qr_frame = tk.Frame(payment_frame, bg=self.ui_config.card_color)
        qr_frame.pack()
        
        # 尝试加载二维码图片
        self._load_qr_code(qr_frame)
        
        # 鼓励文字
        encourage_label = tk.Label(
            main_frame,
            text="每一份支持都是对开源精神的鼓励！\n让我们一起让SolidWorks自动化变得更简单！",
            font=("Microsoft YaHei", 10),
            bg=self.ui_config.bg_color,
            fg=self.ui_config.secondary_text,
            justify=tk.CENTER
        )
        encourage_label.pack(pady=(20, 0))
        
        # 关闭按钮
        close_btn = CustomButton(
            main_frame,
            "关闭",
            self.dialog.destroy,
            style="custom",
            width=10
        )
        close_btn.pack(pady=(30, 0))
    
    def _load_qr_code(self, parent):
        """加载二维码图片"""
        qr_path = "wechat_qr.png"
        
        try:
            if os.path.exists(qr_path):
                # 尝试加载PIL
                try:
                    from PIL import Image, ImageTk
                    
                    # 加载并调整图片大小
                    image = Image.open(qr_path)
                    image = image.resize((200, 200), Image.Resampling.LANCZOS)
                    photo = ImageTk.PhotoImage(image)
                    
                    qr_label = tk.Label(
                        parent,
                        image=photo,
                        bg=self.ui_config.card_color
                    )
                    qr_label.image = photo  # 保持引用
                    qr_label.pack()
                    
                except ImportError:
                    # PIL不可用，显示提示文字
                    self._show_qr_text(parent)
            else:
                # 图片不存在，显示提示
                self._show_qr_text(parent)
                
        except Exception as e:
            app_logger.log_error(e, "加载二维码图片")
            self._show_qr_text(parent)
    
    def _show_qr_text(self, parent):
        """显示二维码文字提示"""
        qr_text_label = tk.Label(
            parent,
            text="📱 二维码区域\n\n请添加二维码图片文件\n'wechat_qr.png'\n到程序目录",
            font=("Microsoft YaHei", 11),
            bg=self.ui_config.card_color,
            fg=self.ui_config.secondary_text,
            justify=tk.CENTER,
            width=25,
            height=8,
            relief=tk.SOLID,
            bd=1
        )
        qr_text_label.pack()


class DebugDialog:
    """调试帮助对话框"""
    
    def __init__(self, parent):
        """
        Args:
            parent: 父窗口
        """
        self.parent = parent
        self.ui_config = config_manager.config.ui
        self.dialog = None
        
        self.question_text = None
        self.answer_text = None
        self.status_label = None
        self.progress = None
    
    def show(self):
        """显示对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("调试帮助")
        self.dialog.geometry("900x700")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        self.dialog.config(bg=self.ui_config.bg_color)
        
        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (900 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (700 // 2)
        self.dialog.geometry(f"900x700+{x}+{y}")
        
        self._create_widgets()
    
    def _create_widgets(self):
        """创建界面控件"""
        # 主框架
        main_frame = tk.Frame(self.dialog, bg=self.ui_config.bg_color, padx=15, pady=15)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 问题输入区域
        question_frame = tk.LabelFrame(
            main_frame,
            text="请输入您的问题或遇到的错误",
            bg=self.ui_config.card_color,
            fg=self.ui_config.text_color,
            font=("Microsoft YaHei", 11, "bold"),
            padx=10,
            pady=10
        )
        question_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 问题文本框
        self.question_text = scrolledtext.ScrolledText(
            question_frame,
            height=6,
            font=("Microsoft YaHei", 10),
            bg=self.ui_config.input_bg,
            fg="white",
            insertbackground="white",
            selectbackground=self.ui_config.accent_color,
            wrap=tk.WORD,
            borderwidth=0,
            relief=tk.FLAT
        )
        self.question_text.pack(fill=tk.X, pady=5)
        
        # 占位符文本
        placeholder = "请详细描述您遇到的问题或错误，例如：\n• 脚本执行时的错误信息\n• 想要实现的功能描述\n• SolidWorks操作相关的疑问"
        self.question_text.insert("1.0", placeholder)
        self.question_text.bind("<FocusIn>", self._clear_placeholder)
        
        # 建议问题区域
        suggestions_frame = tk.LabelFrame(
            main_frame,
            text="常见问题示例",
            bg=self.ui_config.card_color,
            fg=self.ui_config.text_color,
            font=("Microsoft YaHei", 11, "bold"),
            padx=10,
            pady=10
        )
        suggestions_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 建议问题列表
        suggestions = [
            "如何修复 'Object reference not set' 错误？",
            "怎样在SolidWorks中用VBScript创建3D模型？",
            "如何在零件之间添加装配约束？",
            "怎样在SolidWorks模型中使用自定义属性？",
            "为什么我的脚本出现 'Type mismatch' 错误？"
        ]
        
        for i, suggestion in enumerate(suggestions):
            def make_click_handler(sugg):
                return lambda: self._set_question(sugg)
            
            suggestion_btn = tk.Button(
                suggestions_frame,
                text=suggestion,
                font=("Microsoft YaHei", 9),
                bg="#283A5B",
                fg=self.ui_config.text_color,
                relief=tk.FLAT,
                padx=5,
                pady=3,
                cursor="hand2",
                command=make_click_handler(suggestion)
            )
            suggestion_btn.pack(fill=tk.X, pady=2)
            
            # 悬停效果
            suggestion_btn.bind("<Enter>", lambda e, btn=suggestion_btn: btn.config(bg=self.ui_config.accent_color))
            suggestion_btn.bind("<Leave>", lambda e, btn=suggestion_btn: btn.config(bg="#283A5B"))
        
        # 发送按钮和状态
        send_frame = tk.Frame(main_frame, bg=self.ui_config.bg_color)
        send_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 进度指示器
        self.progress = ProgressIndicator(send_frame)
        self.progress.pack(side=tk.RIGHT, padx=5)
        
        # 状态标签
        self.status_label = tk.Label(
            send_frame,
            text="",
            bg=self.ui_config.bg_color,
            fg=self.ui_config.text_color,
            font=("Microsoft YaHei", 10)
        )
        self.status_label.pack(side=tk.RIGHT, padx=5)
        
        # 发送按钮
        send_btn = CustomButton(
            send_frame,
            "发送问题",
            self._send_question,
            style="primary"
        )
        send_btn.pack(side=tk.LEFT, padx=5)
        
        # 答案显示区域
        answer_frame = tk.LabelFrame(
            main_frame,
            text="AI助手回答",
            bg=self.ui_config.card_color,
            fg=self.ui_config.text_color,
            font=("Microsoft YaHei", 11, "bold"),
            padx=10,
            pady=10
        )
        answer_frame.pack(fill=tk.BOTH, expand=True)
        
        # 答案文本框
        self.answer_text = scrolledtext.ScrolledText(
            answer_frame,
            font=("Microsoft YaHei", 10),
            bg=self.ui_config.input_bg,
            fg="white",
            insertbackground="white",
            selectbackground=self.ui_config.accent_color,
            wrap=tk.WORD,
            borderwidth=0,
            relief=tk.FLAT,
            state=tk.DISABLED
        )
        self.answer_text.pack(fill=tk.BOTH, expand=True, pady=5)
    
    def _clear_placeholder(self, event):
        """清除占位符文本"""
        current_text = self.question_text.get("1.0", tk.END).strip()
        if "请详细描述您遇到的问题" in current_text:
            self.question_text.delete("1.0", tk.END)
    
    def _set_question(self, question: str):
        """设置问题文本"""
        self.question_text.delete("1.0", tk.END)
        self.question_text.insert("1.0", question)
    
    def _send_question(self):
        """发送问题"""
        question = self.question_text.get("1.0", tk.END).strip()
        
        if not question or "请详细描述您遇到的问题" in question:
            self.status_label.config(text="请输入您的问题", fg="red")
            return
        
        # 显示处理状态
        self.status_label.config(text="正在获取回答...", fg="white")
        self.progress.start()
        
        # 清空答案区域
        self.answer_text.config(state=tk.NORMAL)
        self.answer_text.delete("1.0", tk.END)
        self.answer_text.insert("1.0", "请稍候，AI助手正在分析您的问题...")
        self.answer_text.config(state=tk.DISABLED)
        
        # 在后台线程中处理
        def process_question():
            try:
                from script_manager import script_manager
                success, answer = script_manager.debugger.provide_guidance(question)
                
                # 在主线程中更新UI
                self.dialog.after(0, lambda: self._show_answer(success, answer))
                
            except Exception as e:
                self.dialog.after(0, lambda: self._show_answer(False, str(e)))
        
        threading.Thread(target=process_question, daemon=True).start()
    
    def _show_answer(self, success: bool, answer: str):
        """显示回答"""
        self.progress.stop()
        
        self.answer_text.config(state=tk.NORMAL)
        self.answer_text.delete("1.0", tk.END)
        
        if success:
            self.answer_text.insert("1.0", answer)
            self.status_label.config(text="回答已获取", fg="lightgreen")
        else:
            self.answer_text.insert("1.0", f"获取回答时出错：{answer}")
            self.status_label.config(text="获取回答失败", fg="red")
        
        self.answer_text.config(state=tk.DISABLED) 