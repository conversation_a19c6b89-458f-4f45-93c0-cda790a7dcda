#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
临时脚本：将微信二维码图片转换为Base64编码
"""

import base64
import os

def image_to_base64(image_path):
    """将图片文件转换为Base64字符串"""
    try:
        with open(image_path, "rb") as image_file:
            base64_string = base64.b64encode(image_file.read()).decode('utf-8')
            return base64_string
    except Exception as e:
        print(f"转换图片时出错: {e}")
        return None

def main():
    image_path = "wechat_qr.png"
    
    if not os.path.exists(image_path):
        print(f"错误: 找不到图片文件 {image_path}")
        return
    
    print("正在转换图片为Base64编码...")
    base64_data = image_to_base64(image_path)
    
    if base64_data:
        # 生成Python代码
        code = f'''# 微信二维码图片的Base64编码数据
WECHAT_QR_BASE64 = """
{base64_data}
"""

def get_wechat_qr_image():
    """获取微信二维码图片数据"""
    import base64
    import io
    try:
        from PIL import Image
        
        # 解码Base64数据
        image_data = base64.b64decode(WECHAT_QR_BASE64)
        
        # 创建PIL图像对象
        image = Image.open(io.BytesIO(image_data))
        return image
    except ImportError:
        # 如果没有PIL，返回原始字节数据
        return base64.b64decode(WECHAT_QR_BASE64)
'''
        
        # 保存到文件
        with open("wechat_qr_data.py", "w", encoding='utf-8') as f:
            f.write(code)
        
        print("✅ 转换完成！")
        print("📁 Base64数据已保存到 wechat_qr_data.py")
        print(f"📊 原图片大小: {os.path.getsize(image_path)} 字节")
        print(f"📊 Base64数据长度: {len(base64_data)} 字符")
    else:
        print("❌ 转换失败")

if __name__ == "__main__":
    main() 