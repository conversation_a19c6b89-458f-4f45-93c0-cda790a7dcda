Option Explicit

' 创建立方体/长方体的SolidWorks脚本
' 此脚本创建一个参数化的立方体或长方体
' 作者: SolidWorks AI助手

' 主要变量
Dim swApp, swModel, swModelDoc, swSketchManager, swFeatureManager
Dim length, width, height

' 获取用户输入的尺寸参数
length = InputBox("请输入长度 (mm):", "立方体参数", "50")
If length = "" Then WScript.Quit(0)
length = CDbl(length) / 1000  ' 转换为米

width = InputBox("请输入宽度 (mm):", "立方体参数", "50") 
If width = "" Then WScript.Quit(0)
width = CDbl(width) / 1000   ' 转换为米

height = InputBox("请输入高度 (mm):", "立方体参数", "50")
If height = "" Then WScript.Quit(0)
height = CDbl(height) / 1000  ' 转换为米

' 连接到SolidWorks
On Error Resume Next
WScript.Echo "正在连接到SolidWorks..."
Set swApp = GetObject(, "SldWorks.Application")

If Err.Number <> 0 Then
    Err.Clear
    WScript.Echo "SolidWorks未运行，尝试启动..."
    Set swApp = CreateObject("SldWorks.Application")
    If Err.Number <> 0 Then
        WScript.Echo "连接SolidWorks时出错: " & Err.Description
        WScript.Quit(1)
    End If
End If

swApp.Visible = True
WScript.Echo "成功连接到SolidWorks。"
On Error Goto 0

' 创建新零件文档
On Error Resume Next
WScript.Echo "正在创建新零件文档..."

Set swModel = swApp.NewPart()
If swModel Is Nothing Then
    WScript.Echo "创建零件文档失败，尝试使用模板..."
    Set swModel = swApp.NewDocument("", 1, 0, 0)
End If

If Err.Number <> 0 Then
    WScript.Echo "创建零件文档时出错: " & Err.Description
    WScript.Quit(1)
End If

If swModel Is Nothing Then
    WScript.Echo "错误: 零件模型未创建。"
    WScript.Quit(1)
End If

Set swModelDoc = swModel
Set swSketchManager = swModelDoc.SketchManager
Set swFeatureManager = swModelDoc.FeatureManager

WScript.Echo "新零件文档创建成功。"
On Error Goto 0

' 选择前视基准面
On Error Resume Next
Dim boolStatus
boolStatus = swModelDoc.Extension.SelectByID2("Front Plane", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
If Not boolStatus Then
    boolStatus = swModelDoc.Extension.SelectByID2("前视基准面", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
End If

If Not boolStatus Then
    WScript.Echo "选择基准面失败"
    WScript.Quit(1)
End If
On Error Goto 0

' 创建草图
WScript.Echo "正在创建草图..."
swSketchManager.InsertSketch True

' 绘制矩形
Dim x1, y1, x2, y2
x1 = -length / 2
y1 = -width / 2  
x2 = length / 2
y2 = width / 2

swSketchManager.CreateCornerRectangle x1, y1, 0, x2, y2, 0
WScript.Echo "创建了 " & (length*1000) & "x" & (width*1000) & " mm的矩形。"

' 结束草图
swSketchManager.InsertSketch True

' 拉伸草图创建立方体
WScript.Echo "正在拉伸草图..."

' 选择草图
swModelDoc.ClearSelection2 True
boolStatus = swModelDoc.Extension.SelectByID2("Sketch1", "SKETCH", 0, 0, 0, False, 0, Nothing, 0)

If boolStatus Then
    ' 创建拉伸特征
    Dim myFeature
    Set myFeature = swFeatureManager.FeatureExtrusion2(True, False, False, 0, 0, height, 0.01, False, 0, False, False, 0, 0, False, False, False, False, False, False, False, False, False, False)
    
    If myFeature Is Nothing Then
        WScript.Echo "拉伸失败，尝试其他方法..."
        ' 使用菜单命令
        swApp.RunCommand 392, ""  ' Insert Boss/Base Extrude
    Else
        WScript.Echo "拉伸成功。"
    End If
Else
    WScript.Echo "选择草图失败"
End If

' 显示等轴测视图并缩放到合适大小
swModelDoc.ShowNamedView2 "*Isometric", 7
swModelDoc.ViewZoomtofit2

' 重命名特征
On Error Resume Next
swModelDoc.Extension.SelectByID2 "Boss-Extrude1", "BODYFEATURE", 0, 0, 0, False, 0, Nothing, 0
swModelDoc.SelectedFeatureProperties 0, 0, 0, 0, 0, 0, 0, True, False, "立方体"
On Error Goto 0

WScript.Echo "SUCCESS: 立方体创建完成！"
WScript.Echo "尺寸: " & (length*1000) & " x " & (width*1000) & " x " & (height*1000) & " mm"
WScript.Quit(0)
