Option Explicit

Dim swApp
On Error Resume Next
WScript.Echo "Connecting to SolidWorks..."
Set swApp = GetObject(, "SldWorks.Application")
If Err.Number <> 0 Then
    Err.Clear
    WScript.Echo "SolidWorks not running, trying to start..."
    Set swApp = CreateObject("SldWorks.Application")
    If Err.Number <> 0 Then
        WScript.Echo "Error connecting to SolidWorks: " & Err.Description
        WScript.Quit(1)
    End If
End If
swApp.Visible = True
WScript.Echo "Successfully connected to SolidWorks."
On Error Goto 0

Dim swModel
Set swModel = swApp.NewPart()
If swModel Is Nothing Then
    WScript.Echo "Failed to create new part document"
    WScript.Quit(1)
End If

Dim swFeatMgr
Set swFeatMgr = swModel.FeatureManager

Dim swSketchMgr
Set swSketchMgr = swModel.SketchManager

' Select front plane
Dim swFeat, bFound
Set swFeat = swModel.FirstFeature()
bFound = False
Do While Not swFeat Is Nothing And bFound = False
    If swFeat.GetTypeName() = "RefPlane" Then
        swModel.ClearSelection2 True
        Dim bRet
        bRet = swFeat.Select2(False, 0)
        If bRet = True Then
            bFound = True
        End If
    End If
    If bFound = False Then
        Set swFeat = swFeat.GetNextFeature()
    End If
Loop

If Not bFound Then
    WScript.Echo "Failed to find reference plane"
    WScript.Quit(1)
End If

' Create rectangle sketch
swSketchMgr.InsertSketch True
swSketchMgr.CreateCornerRectangle 0, 0, 0, 0.1, 0.1, 0
swSketchMgr.InsertSketch True

' Extrude the rectangle
Dim swFeature
Set swFeature = swFeatMgr.FeatureExtrusion2(True, False, False, 0, 0, 0.1, 0.01, False, False, False, False, 0, 0, False, False, False, False, True, True, True, 0, 0, False)
If swFeature Is Nothing Then
    WScript.Echo "Failed to create extrusion feature"
    WScript.Quit(1)
End If

WScript.Echo "Successfully created cube"
WScript.Quit(0)