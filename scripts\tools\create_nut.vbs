Option Explicit

' 创建标准螺母的SolidWorks脚本
' 此脚本创建一个简化的六角螺母
' 作者: SolidWorks AI助手

' 主要变量
Dim swApp, swModel, swModelDoc, swSketchManager, swFeatureManager
Dim threadDiameter, nutHeight, nutWidth

' 获取用户输入的螺母参数
threadDiameter = InputBox("请输入螺纹直径 (mm):", "螺母参数", "8")
If threadDiameter = "" Then WScript.Quit(0)
threadDiameter = CDbl(threadDiameter) / 1000  ' 转换为米

nutHeight = InputBox("请输入螺母高度 (mm):", "螺母参数", "6")
If nutHeight = "" Then WScript.Quit(0)
nutHeight = CDbl(nutHeight) / 1000  ' 转换为米

' 计算六角螺母的对边距离（简化计算）
nutWidth = threadDiameter * 1.5  ' 对边距离约为螺纹直径的1.5倍

' 连接到SolidWorks
On Error Resume Next
WScript.Echo "正在连接到SolidWorks..."
Set swApp = GetObject(, "SldWorks.Application")

If Err.Number <> 0 Then
    Err.Clear
    WScript.Echo "SolidWorks未运行，尝试启动..."
    Set swApp = CreateObject("SldWorks.Application")
    If Err.Number <> 0 Then
        WScript.Echo "连接SolidWorks时出错: " & Err.Description
        WScript.Quit(1)
    End If
End If

swApp.Visible = True
WScript.Echo "成功连接到SolidWorks。"
On Error Goto 0

' 创建新零件文档
On Error Resume Next
WScript.Echo "正在创建新零件文档..."

Set swModel = swApp.NewPart()
If swModel Is Nothing Then
    Set swModel = swApp.NewDocument("", 1, 0, 0)
End If

If swModel Is Nothing Then
    WScript.Echo "错误: 零件模型未创建。"
    WScript.Quit(1)
End If

Set swModelDoc = swModel
Set swSketchManager = swModelDoc.SketchManager
Set swFeatureManager = swModelDoc.FeatureManager

WScript.Echo "新零件文档创建成功。"
On Error Goto 0

' 选择前视基准面
On Error Resume Next
Dim boolStatus
boolStatus = swModelDoc.Extension.SelectByID2("Front Plane", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
If Not boolStatus Then
    boolStatus = swModelDoc.Extension.SelectByID2("前视基准面", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
End If

If Not boolStatus Then
    WScript.Echo "选择基准面失败"
    WScript.Quit(1)
End If
On Error Goto 0

' 创建六角形外形草图
WScript.Echo "正在创建六角形外形..."
swSketchManager.InsertSketch True

' 绘制六角形（简化为圆形）
Dim outerRadius
outerRadius = nutWidth / 2
swSketchManager.CreateCircleByRadius 0, 0, 0, outerRadius

' 创建内部螺纹孔
Dim holeRadius
holeRadius = threadDiameter / 2
swSketchManager.CreateCircleByRadius 0, 0, 0, holeRadius

WScript.Echo "创建了螺母外形，对边距离: " & (nutWidth*1000) & " mm"
WScript.Echo "螺纹孔直径: " & (threadDiameter*1000) & " mm"

' 结束草图
swSketchManager.InsertSketch True

' 拉伸螺母
WScript.Echo "正在拉伸螺母..."

' 选择外圆
swModelDoc.ClearSelection2 True
boolStatus = swModelDoc.Extension.SelectByID2("Arc1", "SKETCHSEGMENT", 0, 0, 0, False, 0, Nothing, 0)

If Not boolStatus Then
    ' 如果选择失败，选择整个草图
    boolStatus = swModelDoc.Extension.SelectByID2("Sketch1", "SKETCH", 0, 0, 0, False, 0, Nothing, 0)
End If

If boolStatus Then
    ' 创建拉伸特征
    Dim nutFeature
    Set nutFeature = swFeatureManager.FeatureExtrusion2(True, False, False, 0, 0, nutHeight, 0.01, False, 0, False, False, 0, 0, False, False, False, False, False, False, False, False, False, False)
    
    If nutFeature Is Nothing Then
        WScript.Echo "螺母拉伸失败，尝试其他方法..."
        swApp.RunCommand 392, ""  ' Insert Boss/Base Extrude
    Else
        WScript.Echo "螺母拉伸成功"
    End If
End If

' 创建螺纹孔
WScript.Echo "正在创建螺纹孔..."

' 选择螺母顶面
swModelDoc.ClearSelection2 True
boolStatus = swModelDoc.Extension.SelectByID2("", "FACE", 0, 0, nutHeight, False, 0, Nothing, 0)

If Not boolStatus Then
    ' 如果选择失败，选择前视基准面
    boolStatus = swModelDoc.Extension.SelectByID2("Front Plane", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
    If Not boolStatus Then
        boolStatus = swModelDoc.Extension.SelectByID2("前视基准面", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
    End If
End If

' 创建孔的草图
swSketchManager.InsertSketch True

' 绘制螺纹孔
swSketchManager.CreateCircleByRadius 0, 0, 0, holeRadius

' 结束草图
swSketchManager.InsertSketch True

' 切除螺纹孔
swModelDoc.ClearSelection2 True
boolStatus = swModelDoc.Extension.SelectByID2("Sketch2", "SKETCH", 0, 0, 0, False, 0, Nothing, 0)

If boolStatus Then
    ' 创建切除特征
    Dim holeFeature
    Set holeFeature = swFeatureManager.FeatureCut2(True, False, False, 0, 0, nutHeight, nutHeight, False, 0, False, False, 0, 0, False, False, False, False, False, False, False, False, False, False, False)
    
    If holeFeature Is Nothing Then
        WScript.Echo "螺纹孔切除失败"
    Else
        WScript.Echo "螺纹孔创建成功"
    End If
End If

' 显示等轴测视图并缩放到合适大小
swModelDoc.ShowNamedView2 "*Isometric", 7
swModelDoc.ViewZoomtofit2

' 重命名特征
On Error Resume Next
swModelDoc.Extension.SelectByID2 "Boss-Extrude1", "BODYFEATURE", 0, 0, 0, False, 0, Nothing, 0
swModelDoc.SelectedFeatureProperties 0, 0, 0, 0, 0, 0, 0, True, False, "螺母本体"

swModelDoc.Extension.SelectByID2 "Cut-Extrude1", "BODYFEATURE", 0, 0, 0, False, 0, Nothing, 0
swModelDoc.SelectedFeatureProperties 0, 0, 0, 0, 0, 0, 0, True, False, "螺纹孔"
On Error Goto 0

WScript.Echo "SUCCESS: 螺母创建完成！"
WScript.Echo "参数: 螺纹直径" & (threadDiameter*1000) & "mm, 高度" & (nutHeight*1000) & "mm"
WScript.Echo "对边距离: " & (nutWidth*1000) & " mm"
WScript.Echo "注意: 这是一个简化的螺母模型，实际螺纹需要专业的螺纹工具。"
WScript.Quit(0)
