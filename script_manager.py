#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本管理模块
处理VBScript的生成、执行、调试和历史记录管理
"""

import os
import shutil
import subprocess
import datetime
import glob
from typing import List, Tuple, Optional
import uuid

from config import config_manager
from logger import app_logger
from api_client import api_service


class ScriptGenerator:
    """VBScript脚本生成器"""
    
    def __init__(self):
        self.config = config_manager.config
    
    def generate_script(self, query: str) -> Tuple[bool, str, Optional[str]]:
        """根据用户请求生成VBScript脚本
        
        Args:
            query: 用户请求文本
            
        Returns:
            (成功状态, 消息, 脚本路径): 脚本生成状态、消息和生成的脚本文件路径
        """
        try:
            if not query or not query.strip():
                return False, "请输入有效的操作描述", None
            
            # 调用API生成脚本
            success, script_content = api_service.generate_script(query)
            
            if not success:
                return False, f"脚本生成失败: {script_content}", None
            
            # 保存脚本到文件
            script_path = config_manager.get_current_script_path()
            
            try:
                # 使用GBK编码保存VBScript文件，以兼容Windows cscript
                with open(script_path, "w", encoding='gbk', errors='replace') as f:
                    f.write(script_content)
                
                app_logger.log_script_generation(query, True)
                return True, "脚本生成成功", script_path
                
            except Exception as e:
                app_logger.log_error(e, "保存脚本文件")
                return False, f"保存脚本文件时出错: {str(e)}", None
                
        except Exception as e:
            app_logger.log_error(e, "脚本生成")
            return False, f"脚本生成过程中出错: {str(e)}", None


class ScriptExecutor:
    """VBScript脚本执行器"""
    
    def __init__(self):
        self.config = config_manager.config
    
    def execute_script(self, script_path: str) -> Tuple[bool, str, str]:
        """执行VBScript脚本
        
        Args:
            script_path: 脚本文件路径
            
        Returns:
            (成功状态, 结果消息, 输出): 执行状态、结果消息和标准输出
        """
        try:
            if not os.path.exists(script_path):
                return False, "脚本文件不存在", ""
            
            # 使用cscript执行VBScript
            cmd = ["cscript", "//NoLogo", script_path]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=False,  # 获取字节格式输出以便手动处理编码
                timeout=60  # 60秒超时
            )
            
            # 手动处理编码，避免编码错误
            try:
                output = result.stdout.decode('utf-8', errors='replace')
            except Exception:
                try:
                    output = result.stdout.decode('gbk', errors='replace')
                except Exception:
                    output = str(result.stdout)
            
            try:
                error_output = result.stderr.decode('utf-8', errors='replace')
            except Exception:
                try:
                    error_output = result.stderr.decode('gbk', errors='replace')
                except Exception:
                    error_output = str(result.stderr)
            
            if result.returncode == 0:
                app_logger.log_script_execution(script_path, True)
                return True, "脚本执行成功", output
            else:
                error_msg = error_output if error_output else f"脚本执行失败，退出代码: {result.returncode}"
                app_logger.log_script_execution(script_path, False, error_msg)
                return False, error_msg, output
                
        except subprocess.TimeoutExpired:
            error_msg = "脚本执行超时（60秒）"
            app_logger.log_script_execution(script_path, False, error_msg)
            return False, error_msg, ""
            
        except FileNotFoundError:
            error_msg = "无法找到cscript.exe，请确保Windows脚本宿主已安装"
            app_logger.log_script_execution(script_path, False, error_msg)
            return False, error_msg, ""
            
        except Exception as e:
            error_msg = f"执行脚本时出错: {str(e)}"
            app_logger.log_error(e, "脚本执行")
            return False, error_msg, ""


class ScriptDebugger:
    """VBScript脚本调试器"""
    
    def __init__(self):
        self.config = config_manager.config
    
    def debug_script(self, script_path: str, error_message: str = "") -> Tuple[bool, str, str]:
        """调试VBScript脚本
        
        Args:
            script_path: 脚本文件路径
            error_message: 错误消息
            
        Returns:
            (成功状态, 修复后的脚本, 解释): 调试状态、修复后的脚本内容和解释
        """
        try:
            if not os.path.exists(script_path):
                return False, "", "脚本文件不存在"
            
            # 读取脚本内容，使用GBK编码
            try:
                with open(script_path, "r", encoding='gbk') as f:
                    script_content = f.read()
            except UnicodeDecodeError:
                # 如果GBK解码失败，尝试UTF-8
                with open(script_path, "r", encoding='utf-8', errors='replace') as f:
                    script_content = f.read()
            
            # 如果没有提供错误消息，先尝试执行脚本获取错误
            if not error_message:
                executor = ScriptExecutor()
                success, error_msg, output = executor.execute_script(script_path)
                if success:
                    return False, "", "脚本运行正常，无需调试"
                error_message = error_msg
            
            # 调用API进行调试
            success, fixed_script, explanation = api_service.debug_script(script_content, error_message)
            
            if success and fixed_script:
                return True, fixed_script, explanation
            else:
                return False, "", explanation or "调试失败"
                
        except Exception as e:
            app_logger.log_error(e, "脚本调试")
            return False, "", f"调试过程中出错: {str(e)}"
    
    def provide_guidance(self, user_query: str) -> Tuple[bool, str]:
        """提供用户指导
        
        Args:
            user_query: 用户问题
            
        Returns:
            (成功状态, 回答): 请求状态和AI助手的回答
        """
        try:
            return api_service.provide_guidance(user_query)
        except Exception as e:
            app_logger.log_error(e, "提供指导")
            return False, f"获取指导时出错: {str(e)}"


class HistoryManager:
    """历史记录管理器"""
    
    def __init__(self):
        self.config = config_manager.config
        self.history_dir = self.config.history_dir
        self.max_history = self.config.max_history
    
    def save_to_history(self, script_content: str, description: str = "") -> str:
        """保存脚本到历史记录
        
        Args:
            script_content: 脚本内容
            description: 描述信息
            
        Returns:
            保存的历史记录文件路径
        """
        try:
            # 生成唯一的文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            filename = f"script_{timestamp}_{unique_id}.vbs"
            file_path = os.path.join(self.history_dir, filename)
            
            # 添加元数据注释
            metadata = f"""' 保存时间: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
' 描述: {description}
' ----------------------------------------

"""
            
            # 保存文件，使用GBK编码以兼容Windows cscript
            with open(file_path, "w", encoding='gbk', errors='replace') as f:
                f.write(metadata + script_content)
            
            # 清理旧的历史记录
            self._cleanup_history()
            
            app_logger.main_logger.info(f"脚本已保存到历史记录: {filename}")
            return file_path
            
        except Exception as e:
            app_logger.log_error(e, "保存历史记录")
            return ""
    
    def get_history_list(self) -> List[Tuple[str, str, str]]:
        """获取历史记录列表
        
        Returns:
            历史记录列表，每项包含 (文件路径, 显示名称, 时间)
        """
        try:
            history_files = []
            pattern = os.path.join(self.history_dir, "script_*.vbs")
            
            for file_path in sorted(glob.glob(pattern), reverse=True):
                try:
                    # 从文件名提取时间信息
                    filename = os.path.basename(file_path)
                    if filename.startswith("script_") and filename.endswith(".vbs"):
                        # 提取时间戳部分
                        name_parts = filename[7:-4].split("_")  # 移除 "script_" 和 ".vbs"
                        if len(name_parts) >= 2:
                            date_part = name_parts[0]
                            time_part = name_parts[1]
                            
                            # 格式化显示时间
                            try:
                                datetime_obj = datetime.datetime.strptime(
                                    f"{date_part}_{time_part}", 
                                    "%Y%m%d_%H%M%S"
                                )
                                display_time = datetime_obj.strftime("%m-%d %H:%M")
                            except ValueError:
                                display_time = date_part
                            
                            # 尝试从文件中读取描述
                            description = self._get_script_description(file_path)
                            
                            display_name = f"{display_time} - {description}" if description else display_time
                            history_files.append((file_path, display_name, display_time))
                            
                except Exception:
                    continue
            
            return history_files[:self.max_history]
            
        except Exception as e:
            app_logger.log_error(e, "获取历史记录列表")
            return []
    
    def _get_script_description(self, file_path: str) -> str:
        """从脚本文件中提取描述信息"""
        try:
            # 使用GBK编码读取，与保存时一致
            try:
                with open(file_path, "r", encoding='gbk') as f:
                    lines = f.readlines()
            except UnicodeDecodeError:
                # 如果GBK解码失败，尝试UTF-8
                with open(file_path, "r", encoding='utf-8', errors='replace') as f:
                    lines = f.readlines()
                
            for line in lines[:10]:  # 只检查前10行
                line = line.strip()
                if line.startswith("' 描述:"):
                    description = line[5:].strip()  # 移除 "' 描述:" 前缀
                    if description and description != "无":
                        return description[:30]  # 限制长度
                        
            return "脚本记录"
            
        except Exception:
            return "脚本记录"
    
    def _cleanup_history(self) -> None:
        """清理旧的历史记录，保持数量限制"""
        try:
            pattern = os.path.join(self.history_dir, "script_*.vbs")
            history_files = sorted(glob.glob(pattern), reverse=True)
            
            # 删除超出限制的文件
            if len(history_files) > self.max_history:
                for file_path in history_files[self.max_history:]:
                    try:
                        os.remove(file_path)
                        app_logger.main_logger.info(f"删除旧的历史记录: {os.path.basename(file_path)}")
                    except Exception as e:
                        app_logger.log_error(e, f"删除历史记录文件 {file_path}")
                        
        except Exception as e:
            app_logger.log_error(e, "清理历史记录")
    
    def load_script_from_history(self, file_path: str) -> Tuple[bool, str]:
        """从历史记录加载脚本
        
        Args:
            file_path: 历史记录文件路径
            
        Returns:
            (成功状态, 脚本内容): 加载状态和脚本内容
        """
        try:
            if not os.path.exists(file_path):
                return False, "历史记录文件不存在"
            
            # 读取文件内容，使用GBK编码
            try:
                with open(file_path, "r", encoding='gbk') as f:
                    content = f.read()
            except UnicodeDecodeError:
                # 如果GBK解码失败，尝试UTF-8
                with open(file_path, "r", encoding='utf-8', errors='replace') as f:
                    content = f.read()
            
            # 移除元数据注释，只保留实际脚本内容
            lines = content.split('\n')
            script_start = 0
            
            for i, line in enumerate(lines):
                if line.strip() == "' ----------------------------------------":
                    script_start = i + 1
                    break
            
            if script_start > 0:
                script_content = '\n'.join(lines[script_start:])
            else:
                script_content = content
            
            return True, script_content
            
        except Exception as e:
            app_logger.log_error(e, f"加载历史记录 {file_path}")
            return False, f"加载历史记录时出错: {str(e)}"


class ScriptManager:
    """脚本管理器 - 统一管理脚本相关操作"""
    
    def __init__(self):
        self.generator = ScriptGenerator()
        self.executor = ScriptExecutor()
        self.debugger = ScriptDebugger()
        self.history = HistoryManager()
        self.config = config_manager.config
        
        # 确保当前脚本存在
        config_manager.ensure_current_script_exists()
    
    def generate_and_save_script(self, query: str, save_to_history: bool = True) -> Tuple[bool, str, Optional[str]]:
        """生成脚本并可选保存到历史记录"""
        success, message, script_path = self.generator.generate_script(query)
        
        if success and save_to_history and script_path:
            try:
                # 读取生成的脚本内容，使用GBK编码（与保存时一致）
                try:
                    with open(script_path, "r", encoding='gbk') as f:
                        script_content = f.read()
                except UnicodeDecodeError:
                    # 如果GBK解码失败，尝试UTF-8作为备选
                    with open(script_path, "r", encoding='utf-8', errors='replace') as f:
                        script_content = f.read()
                
                # 保存到历史记录
                self.history.save_to_history(script_content, query[:50])
                
            except Exception as e:
                app_logger.log_error(e, "保存脚本到历史记录")
        
        return success, message, script_path
    
    def execute_current_script(self) -> Tuple[bool, str, str]:
        """执行当前脚本"""
        script_path = config_manager.get_current_script_path()
        return self.executor.execute_script(script_path)
    
    def debug_current_script(self, error_message: str = "") -> Tuple[bool, str, str]:
        """调试当前脚本"""
        script_path = config_manager.get_current_script_path()
        return self.debugger.debug_script(script_path, error_message)
    
    def apply_debug_fix(self, fixed_script: str) -> bool:
        """应用调试修复，更新当前脚本"""
        try:
            script_path = config_manager.get_current_script_path()
            
            # 使用GBK编码保存修复后的脚本
            with open(script_path, "w", encoding='gbk', errors='replace') as f:
                f.write(fixed_script)
            
            app_logger.main_logger.info("调试修复已应用到当前脚本")
            return True
            
        except Exception as e:
            app_logger.log_error(e, "应用调试修复")
            return False
    
    def load_sample_script(self) -> bool:
        """加载示例脚本"""
        try:
            sample_path = config_manager.get_sample_script_path()
            current_path = config_manager.get_current_script_path()
            
            if os.path.exists(sample_path):
                shutil.copy2(sample_path, current_path)
                app_logger.main_logger.info("示例脚本已加载")
                return True
            else:
                app_logger.main_logger.error("示例脚本文件不存在")
                return False
                
        except Exception as e:
            app_logger.log_error(e, "加载示例脚本")
            return False
    
    def get_current_script_content(self) -> str:
        """获取当前脚本内容"""
        try:
            script_path = config_manager.get_current_script_path()
            
            if os.path.exists(script_path):
                # 读取脚本内容，使用GBK编码
                try:
                    with open(script_path, "r", encoding='gbk') as f:
                        return f.read()
                except UnicodeDecodeError:
                    # 如果GBK解码失败，尝试UTF-8
                    with open(script_path, "r", encoding='utf-8', errors='replace') as f:
                        return f.read()
            else:
                return ""
                
        except Exception as e:
            app_logger.log_error(e, "读取当前脚本内容")
            return ""


# 全局脚本管理器实例
script_manager = ScriptManager() 