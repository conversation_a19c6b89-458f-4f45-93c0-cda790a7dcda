Option Explicit

' 创建标准螺栓的SolidWorks脚本
' 此脚本创建一个简化的标准螺栓
' 作者: SolidWorks AI助手

' 主要变量
Dim swApp, swModel, swModelDoc, swSketchManager, swFeatureManager
Dim diameter, length, headDiameter, headHeight

' 获取用户输入的螺栓参数
diameter = InputBox("请输入螺栓直径 (mm):", "螺栓参数", "8")
If diameter = "" Then WScript.Quit(0)
diameter = CDbl(diameter) / 1000  ' 转换为米

length = InputBox("请输入螺栓长度 (mm):", "螺栓参数", "30")
If length = "" Then WScript.Quit(0)
length = CDbl(length) / 1000  ' 转换为米

' 计算螺栓头部尺寸（简化计算）
headDiameter = diameter * 1.5  ' 头部直径约为螺栓直径的1.5倍
headHeight = diameter * 0.7    ' 头部高度约为螺栓直径的0.7倍

' 连接到SolidWorks
On Error Resume Next
WScript.Echo "正在连接到SolidWorks..."
Set swApp = GetObject(, "SldWorks.Application")

If Err.Number <> 0 Then
    Err.Clear
    WScript.Echo "SolidWorks未运行，尝试启动..."
    Set swApp = CreateObject("SldWorks.Application")
    If Err.Number <> 0 Then
        WScript.Echo "连接SolidWorks时出错: " & Err.Description
        WScript.Quit(1)
    End If
End If

swApp.Visible = True
WScript.Echo "成功连接到SolidWorks。"
On Error Goto 0

' 创建新零件文档
On Error Resume Next
WScript.Echo "正在创建新零件文档..."

Set swModel = swApp.NewPart()
If swModel Is Nothing Then
    Set swModel = swApp.NewDocument("", 1, 0, 0)
End If

If swModel Is Nothing Then
    WScript.Echo "错误: 零件模型未创建。"
    WScript.Quit(1)
End If

Set swModelDoc = swModel
Set swSketchManager = swModelDoc.SketchManager
Set swFeatureManager = swModelDoc.FeatureManager

WScript.Echo "新零件文档创建成功。"
On Error Goto 0

' 创建螺栓杆部
WScript.Echo "正在创建螺栓杆部..."

' 选择前视基准面
On Error Resume Next
Dim boolStatus
boolStatus = swModelDoc.Extension.SelectByID2("Front Plane", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
If Not boolStatus Then
    boolStatus = swModelDoc.Extension.SelectByID2("前视基准面", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
End If

If Not boolStatus Then
    WScript.Echo "选择基准面失败"
    WScript.Quit(1)
End If
On Error Goto 0

' 创建草图
swSketchManager.InsertSketch True

' 绘制螺栓杆部圆形
swSketchManager.CreateCircleByRadius 0, 0, 0, diameter / 2
WScript.Echo "创建了螺栓杆部，直径: " & (diameter*1000) & " mm"

' 结束草图
swSketchManager.InsertSketch True

' 拉伸螺栓杆部
swModelDoc.ClearSelection2 True
boolStatus = swModelDoc.Extension.SelectByID2("Sketch1", "SKETCH", 0, 0, 0, False, 0, Nothing, 0)

If boolStatus Then
    Dim shaftFeature
    Set shaftFeature = swFeatureManager.FeatureExtrusion2(True, False, False, 0, 0, length, 0.01, False, 0, False, False, 0, 0, False, False, False, False, False, False, False, False, False, False)
    
    If shaftFeature Is Nothing Then
        WScript.Echo "螺栓杆部拉伸失败"
    Else
        WScript.Echo "螺栓杆部创建成功"
    End If
End If

' 创建螺栓头部
WScript.Echo "正在创建螺栓头部..."

' 选择螺栓杆部的顶面
swModelDoc.ClearSelection2 True
boolStatus = swModelDoc.Extension.SelectByID2("", "FACE", 0, 0, length, False, 0, Nothing, 0)

If Not boolStatus Then
    ' 如果选择失败，选择前视基准面
    boolStatus = swModelDoc.Extension.SelectByID2("Front Plane", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
    If Not boolStatus Then
        boolStatus = swModelDoc.Extension.SelectByID2("前视基准面", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
    End If
End If

' 创建头部草图
swSketchManager.InsertSketch True

' 绘制六角形头部（简化为圆形）
swSketchManager.CreateCircleByRadius 0, 0, 0, headDiameter / 2
WScript.Echo "创建了螺栓头部，直径: " & (headDiameter*1000) & " mm"

' 结束草图
swSketchManager.InsertSketch True

' 拉伸螺栓头部
swModelDoc.ClearSelection2 True
boolStatus = swModelDoc.Extension.SelectByID2("Sketch2", "SKETCH", 0, 0, 0, False, 0, Nothing, 0)

If boolStatus Then
    Dim headFeature
    Set headFeature = swFeatureManager.FeatureExtrusion2(True, False, False, 0, 0, headHeight, 0.01, False, 0, False, False, 0, 0, False, False, False, False, False, False, False, False, False, False)
    
    If headFeature Is Nothing Then
        WScript.Echo "螺栓头部拉伸失败"
    Else
        WScript.Echo "螺栓头部创建成功"
    End If
End If

' 显示等轴测视图并缩放到合适大小
swModelDoc.ShowNamedView2 "*Isometric", 7
swModelDoc.ViewZoomtofit2

' 重命名特征
On Error Resume Next
swModelDoc.Extension.SelectByID2 "Boss-Extrude1", "BODYFEATURE", 0, 0, 0, False, 0, Nothing, 0
swModelDoc.SelectedFeatureProperties 0, 0, 0, 0, 0, 0, 0, True, False, "螺栓杆"

swModelDoc.Extension.SelectByID2 "Boss-Extrude2", "BODYFEATURE", 0, 0, 0, False, 0, Nothing, 0
swModelDoc.SelectedFeatureProperties 0, 0, 0, 0, 0, 0, 0, True, False, "螺栓头"
On Error Goto 0

WScript.Echo "SUCCESS: 螺栓创建完成！"
WScript.Echo "参数: 直径" & (diameter*1000) & "mm, 长度" & (length*1000) & "mm"
WScript.Echo "注意: 这是一个简化的螺栓模型，实际螺纹需要专业的螺纹工具。"
WScript.Quit(0)
