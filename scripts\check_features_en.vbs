Option Explicit

Dim swApp
Set swApp = GetObject(, "SldWorks.Application")
swApp.Visible = True

Dim swModel
Set swModel = swApp.ActiveDoc
If swModel Is Nothing Then
    WScript.Echo "No active document"
    WScript.Quit(1)
End If

WScript.Echo "Document: " & swModel.GetTitle()

Dim swFeat
Set swFeat = swModel.FirstFeature()

WScript.Echo "Features in the document:"
Do While Not swFeat Is Nothing
    WScript.Echo "- " & swFeat.Name & " (Type: " & swFeat.GetTypeName() & ")"
    Set swFeat = swFeat.GetNextFeature()
Loop

WScript.Quit(0)
