# SolidWorks AI 助手配置文件示例
# 复制此文件为 .env 并填入您的实际配置

# ===== API 配置 =====
# 您的AI服务API密钥
OPENAI_API_KEY=your_api_key_here

# API服务地址
OPENAI_BASE_URL=https://api.deepseek.com/v1/chat/completions

# AI模型名称
OPENAI_MODEL=deepseek-chat

# 模型提供商 (支持的值见下方说明)
OPENAI_PROVIDER=deepseek

# 日志级别
LOG_LEVEL=INFO

# ===== 支持的国内大模型提供商 =====
#
# 1. DeepSeek (deepseek) - 推荐
#    Base URL: https://api.deepseek.com/v1/chat/completions
#    Models: deepseek-chat, deepseek-coder
#
# 2. 豆包/字节跳动 (doubao)
#    Base URL: https://ark.cn-beijing.volces.com/api/v3/chat/completions
#    Models: ep-20241230114937-lbxgk, ep-20241230114725-d8qxz
#
# 3. 文心一言/百度 (wenxin)
#    Base URL: https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions
#    Models: ernie-4.0-8k, ernie-3.5-8k, ernie-turbo-8k
#
# 4. 通义千问/阿里 (tongyi)
#    Base URL: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions
#    Models: qwen-turbo, qwen-plus, qwen-max
#
# 5. 智谱AI (zhipu)
#    Base URL: https://open.bigmodel.cn/api/paas/v4/chat/completions
#    Models: glm-4, glm-4-air, glm-3-turbo
#
# 6. Moonshot AI/Kimi (moonshot)
#    Base URL: https://api.moonshot.cn/v1/chat/completions
#    Models: moonshot-v1-8k, moonshot-v1-32k, moonshot-v1-128k
#
# 7. MiniMax (minimax)
#    Base URL: https://api.minimax.chat/v1/text/chatcompletion_v2
#    Models: abab6.5s-chat, abab6.5-chat, abab5.5-chat
#
# 8. OpenAI (openai)
#    Base URL: https://api.openai.com/v1/chat/completions
#    Models: gpt-4, gpt-4-turbo, gpt-3.5-turbo
#
# 9. 自定义 (custom)
#    自定义API地址和模型

# ===== 使用说明 =====
# 1. 将此文件重命名为 .env
# 2. 根据您选择的AI服务商填写相应的配置
# 3. 在应用程序中使用"API设置"功能可以更方便地配置
# 4. 配置保存后会自动生效 