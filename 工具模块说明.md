# 🛠️ SolidWorks AI助手 - 工具模块

## 概述

工具模块是SolidWorks AI助手的新增功能，提供了一系列快捷的3D建模工具和便捷操作，让用户能够快速创建常用的几何体和执行常见的SolidWorks操作。

## 功能特性

### 🎯 快捷建模工具
- **一键创建**: 无需编写代码，点击即可创建常用几何体
- **参数化设计**: 所有工具都支持自定义参数
- **智能提示**: 提供合理的默认值和参数说明
- **错误处理**: 完善的错误处理和用户提示

### 📋 工具分类

#### 1. 基础几何体
- **📦 立方体**: 创建参数化的立方体/长方体
- **🥫 圆柱体**: 创建参数化的圆柱体
- **⚽ 球体**: 创建参数化的球体
- **🔺 圆锥体**: 创建参数化的圆锥体

#### 2. 机械零件
- **⚙️ 直齿圆柱齿轮**: 创建标准直齿圆柱齿轮（简化版）
- **🔩 螺栓**: 创建标准螺栓（简化版）
- **🔧 螺母**: 创建标准螺母（简化版）
- **🌀 弹簧**: 创建螺旋弹簧（简化版）

#### 3. 便捷工具
- **📏 测量工具**: 快速测量距离、角度、面积、体积
- **🧱 材料设置**: 快速设置常用材料属性
- **👁️ 视图工具**: 快速切换标准视图
- **📤 导出工具**: 批量导出为常用格式

## 使用方法

### 1. 启动工具模块
1. 运行SolidWorks AI助手
2. 点击主界面上的"🛠️ 工具箱"选项卡
3. 浏览不同分类的工具

### 2. 使用快捷建模工具
1. 在工具箱中选择所需的几何体类型
2. 点击"使用"按钮
3. 在弹出的对话框中输入参数
4. 工具将自动在SolidWorks中创建模型

### 3. 使用便捷工具
1. 确保SolidWorks中有打开的文档
2. 选择相应的便捷工具
3. 按照提示进行操作

## 工具详细说明

### 基础几何体工具

#### 立方体工具
- **参数**: 长度、宽度、高度（单位：mm）
- **功能**: 创建参数化的长方体
- **输出**: 在SolidWorks中创建拉伸特征

#### 圆柱体工具
- **参数**: 半径、高度（单位：mm）
- **功能**: 创建参数化的圆柱体
- **输出**: 在SolidWorks中创建拉伸特征

#### 球体工具
- **参数**: 半径（单位：mm）
- **功能**: 创建参数化的球体
- **输出**: 在SolidWorks中创建旋转特征

#### 圆锥体工具
- **参数**: 底面半径、高度（单位：mm）
- **功能**: 创建参数化的圆锥体
- **输出**: 在SolidWorks中创建旋转特征

### 机械零件工具

#### 齿轮工具
- **参数**: 齿数、模数、厚度
- **功能**: 创建简化的直齿圆柱齿轮
- **注意**: 这是简化模型，实际齿形需要专业工具

#### 螺栓工具
- **参数**: 直径、长度（单位：mm）
- **功能**: 创建简化的螺栓模型
- **注意**: 不包含实际螺纹，仅为外形

#### 螺母工具
- **参数**: 螺纹直径、高度（单位：mm）
- **功能**: 创建简化的六角螺母
- **注意**: 不包含实际螺纹，仅为外形

#### 弹簧工具
- **参数**: 丝径、外径、长度、圈数
- **功能**: 创建螺旋弹簧模型
- **注意**: 简化模型，可能需要手动调整

### 便捷工具

#### 测量工具
- **功能**: 
  - 距离测量
  - 角度测量
  - 面积计算
  - 体积计算
  - 质量属性分析

#### 材料设置工具
- **支持材料**:
  - 钢 (AISI 1020)
  - 铝合金 (1060 Alloy)
  - 铜 (Copper)
  - 塑料 (ABS PC)
  - 不锈钢 (AISI 316)
  - 铸铁 (Cast Carbon Steel)
  - 黄铜 (Brass)

#### 视图工具
- **支持视图**:
  - 等轴测视图
  - 前视图、后视图
  - 左视图、右视图
  - 俯视图、仰视图
  - 缩放到合适大小
  - 显示/隐藏基准面

#### 导出工具
- **支持格式**:
  - STEP (.step)
  - IGES (.igs)
  - STL (.stl)
  - 3D PDF (.pdf)
  - Parasolid (.x_t)
  - VRML (.wrl)
  - 3D XML (.3dxml)
  - 批量导出

## 技术架构

### 文件结构
```
├── ui/
│   └── tools_panel.py          # 工具面板UI组件
├── scripts/tools/              # 工具脚本目录
│   ├── create_box.vbs         # 立方体创建脚本
│   ├── create_cylinder.vbs    # 圆柱体创建脚本
│   ├── create_sphere.vbs      # 球体创建脚本
│   ├── create_cone.vbs        # 圆锥体创建脚本
│   ├── create_spur_gear.vbs   # 齿轮创建脚本
│   ├── create_bolt.vbs        # 螺栓创建脚本
│   ├── create_nut.vbs         # 螺母创建脚本
│   ├── create_spring.vbs      # 弹簧创建脚本
│   ├── measurement_tool.vbs   # 测量工具脚本
│   ├── material_tool.vbs      # 材料设置脚本
│   ├── view_tool.vbs          # 视图工具脚本
│   └── export_tool.vbs        # 导出工具脚本
├── tools_manager.py           # 工具管理器
└── test_tools.py             # 工具模块测试脚本
```

### 核心组件

#### ToolsManager
- 管理所有工具的注册和执行
- 提供工具验证和状态检查
- 处理工具脚本的执行和错误处理

#### ToolsPanel
- 提供图形化的工具选择界面
- 支持分类浏览和工具搜索
- 集成到主应用程序的选项卡中

## 注意事项

1. **SolidWorks要求**: 需要SolidWorks 2020或更高版本
2. **脚本执行**: 工具脚本使用VBScript编写，通过cscript执行
3. **简化模型**: 机械零件工具创建的是简化模型，实际应用可能需要进一步完善
4. **参数验证**: 输入参数会进行基本验证，但建议输入合理的数值
5. **错误处理**: 如果工具执行失败，会显示详细的错误信息

## 扩展开发

### 添加新工具
1. 在`scripts/tools/`目录下创建VBScript文件
2. 在`tools_manager.py`中注册新工具
3. 在`ui/tools_panel.py`中添加工具分类（如需要）

### 自定义工具分类
可以在`ToolsPanel`类中修改`tool_categories`字典来自定义工具分类和布局。

## 更新日志

### v1.0.0 (2024-12-19)
- 初始版本发布
- 支持12种常用工具
- 集成到主应用程序
- 完整的错误处理和用户提示
