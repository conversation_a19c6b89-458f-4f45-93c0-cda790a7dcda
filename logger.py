#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志记录模块
提供统一的日志记录功能
"""

import sys
import logging
import os
from typing import Optional
from datetime import datetime


class LoggerSetup:
    """日志设置类"""
    
    def __init__(self):
        self._loggers = {}
    
    def setup_logger(
        self,
        name: str,
        log_file: str = "app.log",
        log_level: str = "INFO",
        console_output: bool = True
    ) -> logging.Logger:
        """设置日志记录器
        
        Args:
            name: 日志记录器名称
            log_file: 日志文件路径
            log_level: 日志级别
            console_output: 是否输出到控制台
            
        Returns:
            配置好的日志记录器
        """
        if name in self._loggers:
            return self._loggers[name]
        
        # 创建日志记录器
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, log_level.upper()))
        
        # 清除现有的处理器
        logger.handlers.clear()
        
        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 文件处理器
        try:
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(getattr(logging, log_level.upper()))
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        except Exception as e:
            print(f"创建文件日志处理器时出错: {e}")
        
        # 控制台处理器
        if console_output:
            try:
                # 在Windows打包环境中处理编码问题
                if sys.platform.startswith('win'):
                    # 检查是否有可用的stdout（在PyInstaller打包的程序中可能为None）
                    if hasattr(sys.stdout, 'buffer') and sys.stdout.buffer is not None:
                        import codecs
                        console_handler = logging.StreamHandler(
                            codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
                        )
                    else:
                        # 在无控制台环境中，跳过控制台处理器
                        console_handler = None
                else:
                    console_handler = logging.StreamHandler(sys.stdout)
                
                if console_handler:
                    console_handler.setLevel(getattr(logging, log_level.upper()))
                    console_handler.setFormatter(formatter)
                    logger.addHandler(console_handler)
                    
            except Exception as e:
                # 在打包环境中忽略控制台输出错误
                pass
        
        # 防止重复处理
        logger.propagate = False
        
        # 缓存日志记录器
        self._loggers[name] = logger
        
        return logger
    
    def get_logger(self, name: str) -> Optional[logging.Logger]:
        """获取已配置的日志记录器"""
        return self._loggers.get(name)


class AppLogger:
    """应用程序日志管理器"""
    
    def __init__(self, log_file: str = "sw_api_panel.log", log_level: str = "INFO"):
        self.setup = LoggerSetup()
        self.main_logger = self.setup.setup_logger(
            "SolidWorksAI",
            log_file,
            log_level,
            console_output=True
        )
        
        # 模块专用日志记录器
        self.api_logger = self.setup.setup_logger(
            "SolidWorksAI.API",
            log_file,
            log_level,
            console_output=False
        )
        
        self.ui_logger = self.setup.setup_logger(
            "SolidWorksAI.UI", 
            log_file,
            log_level,
            console_output=False
        )
        
        self.script_logger = self.setup.setup_logger(
            "SolidWorksAI.Script",
            log_file,
            log_level,
            console_output=False
        )
        
        self.config_logger = self.setup.setup_logger(
            "SolidWorksAI.Config",
            log_file,
            log_level,
            console_output=False
        )
    
    def log_api_request(self, method: str, url: str, status_code: Optional[int] = None):
        """记录API请求"""
        if status_code:
            self.api_logger.info(f"API {method} {url} - 状态码: {status_code}")
        else:
            self.api_logger.info(f"API {method} {url} - 请求发送")
    
    def log_api_error(self, method: str, url: str, error: str):
        """记录API错误"""
        self.api_logger.error(f"API {method} {url} - 错误: {error}")
    
    def log_script_generation(self, query: str, success: bool):
        """记录脚本生成"""
        if success:
            self.script_logger.info(f"脚本生成成功 - 查询: {query[:50]}...")
        else:
            self.script_logger.error(f"脚本生成失败 - 查询: {query[:50]}...")
    
    def log_script_execution(self, script_path: str, success: bool, result: str = ""):
        """记录脚本执行"""
        if success:
            self.script_logger.info(f"脚本执行成功 - 路径: {script_path}")
        else:
            self.script_logger.error(f"脚本执行失败 - 路径: {script_path}, 错误: {result}")
    
    def log_ui_action(self, action: str, details: str = ""):
        """记录UI操作"""
        self.ui_logger.info(f"UI操作: {action} {details}")
    
    def log_config_change(self, config_item: str, new_value: str = "***"):
        """记录配置更改"""
        self.config_logger.info(f"配置更改: {config_item} = {new_value}")
    
    def log_error(self, error: Exception, context: str = ""):
        """记录错误"""
        self.main_logger.error(f"错误 {context}: {str(error)}", exc_info=True)
    
    def log_startup(self):
        """记录应用启动"""
        self.main_logger.info("=" * 50)
        self.main_logger.info(f"SolidWorks AI 助手启动 - {datetime.now()}")
        self.main_logger.info("=" * 50)
    
    def log_shutdown(self):
        """记录应用关闭"""
        self.main_logger.info("SolidWorks AI 助手关闭")
        self.main_logger.info("=" * 50)


# 全局日志管理器实例
app_logger = AppLogger() 