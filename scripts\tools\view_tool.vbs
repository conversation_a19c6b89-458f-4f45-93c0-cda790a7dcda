Option Explicit

' SolidWorks视图工具脚本
' 提供快速视图切换功能
' 作者: SolidWorks AI助手

' 主要变量
Dim swApp, swModel, swModelDoc

' 连接到SolidWorks
On Error Resume Next
WScript.Echo "正在连接到SolidWorks..."
Set swApp = GetObject(, "SldWorks.Application")

If Err.Number <> 0 Then
    Err.Clear
    WScript.Echo "SolidWorks未运行，尝试启动..."
    Set swApp = CreateObject("SldWorks.Application")
    If Err.Number <> 0 Then
        WScript.Echo "连接SolidWorks时出错: " & Err.Description
        WScript.Quit(1)
    End If
End If

swApp.Visible = True
WScript.Echo "成功连接到SolidWorks。"
On Error Goto 0

' 获取当前活动文档
Set swModel = swApp.ActiveDoc
If swModel Is Nothing Then
    WScript.Echo "没有打开的文档。请先打开一个SolidWorks文档。"
    WScript.Quit(1)
End If

Set swModelDoc = swModel

' 显示视图选择菜单
Dim choice
choice = InputBox("请选择视图:" & vbCrLf & _
                 "1 - 等轴测视图" & vbCrLf & _
                 "2 - 前视图" & vbCrLf & _
                 "3 - 后视图" & vbCrLf & _
                 "4 - 左视图" & vbCrLf & _
                 "5 - 右视图" & vbCrLf & _
                 "6 - 俯视图" & vbCrLf & _
                 "7 - 仰视图" & vbCrLf & _
                 "8 - 缩放到合适大小" & vbCrLf & _
                 "9 - 显示/隐藏基准面", "视图工具", "1")

If choice = "" Then WScript.Quit(0)

Select Case CInt(choice)
    Case 1
        ' 等轴测视图
        WScript.Echo "切换到等轴测视图..."
        swModelDoc.ShowNamedView2 "*Isometric", 7
        swModelDoc.ViewZoomtofit2
        
    Case 2
        ' 前视图
        WScript.Echo "切换到前视图..."
        swModelDoc.ShowNamedView2 "*Front", 1
        swModelDoc.ViewZoomtofit2
        
    Case 3
        ' 后视图
        WScript.Echo "切换到后视图..."
        swModelDoc.ShowNamedView2 "*Back", 2
        swModelDoc.ViewZoomtofit2
        
    Case 4
        ' 左视图
        WScript.Echo "切换到左视图..."
        swModelDoc.ShowNamedView2 "*Left", 3
        swModelDoc.ViewZoomtofit2
        
    Case 5
        ' 右视图
        WScript.Echo "切换到右视图..."
        swModelDoc.ShowNamedView2 "*Right", 4
        swModelDoc.ViewZoomtofit2
        
    Case 6
        ' 俯视图
        WScript.Echo "切换到俯视图..."
        swModelDoc.ShowNamedView2 "*Top", 5
        swModelDoc.ViewZoomtofit2
        
    Case 7
        ' 仰视图
        WScript.Echo "切换到仰视图..."
        swModelDoc.ShowNamedView2 "*Bottom", 6
        swModelDoc.ViewZoomtofit2
        
    Case 8
        ' 缩放到合适大小
        WScript.Echo "缩放到合适大小..."
        swModelDoc.ViewZoomtofit2
        
    Case 9
        ' 显示/隐藏基准面
        WScript.Echo "切换基准面显示状态..."
        
        ' 获取当前基准面显示状态
        Dim currentState
        currentState = swModelDoc.GetUserPreferenceToggle(swUserPreferenceToggle_e.swViewDisplayDatums)
        
        ' 切换状态
        swModelDoc.SetUserPreferenceToggle swUserPreferenceToggle_e.swViewDisplayDatums, Not currentState
        
        If currentState Then
            WScript.Echo "基准面已隐藏"
        Else
            WScript.Echo "基准面已显示"
        End If
        
        ' 刷新视图
        swModelDoc.GraphicsRedraw2
        
    Case Else
        WScript.Echo "无效的选择。"
        WScript.Quit(1)
End Select

WScript.Echo "SUCCESS: 视图切换完成。"
WScript.Quit(0)
