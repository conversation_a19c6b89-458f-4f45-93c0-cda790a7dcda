# SolidWorks AI助手 使用说明

## 项目简介

SolidWorks AI助手是一个基于人工智能的SolidWorks辅助工具，可以帮助用户通过自然语言生成VBScript脚本来自动化SolidWorks操作。

## 更新内容

### 🎯 主要更新
1. **界面中文化**: 所有界面元素和提示信息已全部翻译为中文
2. **脚本中文化**: scripts目录中的所有VBScript文件注释已翻译为中文
3. **架构重构**: 项目已按照SOLID原则完全重构为模块化架构
4. **编译版本**: 提供独立的exe可执行文件，无需安装Python环境

### 📁 项目结构
```
AI-SolidWorks-main/
├── dist/                           # 编译后的exe文件目录
│   └── SolidWorks-AI助手.exe       # 独立可执行文件 (23MB)
├── main.py                         # 新版主程序入口
├── config.py                       # 配置管理模块
├── logger.py                       # 日志记录模块
├── api_client.py                   # API客户端模块
├── script_manager.py               # 脚本管理模块
├── ui/                             # UI组件模块
│   ├── components.py               # 可复用UI组件
│   └── dialogs.py                  # 对话框组件
├── scripts/                        # VBScript脚本文件(中文注释)
│   ├── connect_to_sw.vbs          # SolidWorks连接脚本
│   ├── create_simple_part.vbs     # 创建简单零件
│   ├── draw_circle.vbs            # 绘制圆形
│   ├── create_sketch.vbs          # 创建草图
│   ├── create_extrude.vbs         # 创建拉伸
│   └── create_sketch_from_input.vbs # 根据参数创建草图
├── 启动SolidWorks-AI助手.bat        # 快速启动批处理文件
└── sw_api_panel.py                 # 原版本(保留)
```

## 使用方法

### 方法一：使用编译版本（推荐）
1. **双击运行**: 直接双击 `dist/SolidWorks-AI助手.exe`
2. **批处理启动**: 双击 `启动SolidWorks-AI助手.bat`

### 方法二：使用Python源码
```bash
# 安装依赖
pip install -r requirements.txt

# 运行新版本
python main.py

# 或运行原版本
python sw_api_panel.py
```

# 编译项目

pyinstaller build_config.spec --distpath ./dist --workpath ./build

## 功能特性

### 🤖 AI功能
- **智能脚本生成**: 通过自然语言描述生成VBScript代码
- **脚本调试**: AI辅助调试VBScript错误
- **操作指导**: 提供SolidWorks操作建议和帮助

### 🔧 核心功能
- **脚本执行**: 直接运行生成的VBScript代码
- **历史管理**: 保存和管理脚本执行历史
- **API配置**: 支持多种AI API服务（默认DeepSeek）
- **界面友好**: 完全中文化的用户界面

### 📝 脚本模板
项目包含多个预制的VBScript模板：
- **连接脚本**: 自动连接SolidWorks应用
- **几何创建**: 创建圆形、矩形、直线等基本图形
- **特征操作**: 拉伸、旋转等三维特征
- **参数化建模**: 支持参数输入的动态建模

## API配置

### 支持的国内大模型
- **DeepSeek** (默认推荐) - 性价比高，编程能力强
- **豆包(字节跳动)** - 字节跳动的大模型服务
- **文心一言(百度)** - 百度的AI助手
- **通义千问(阿里)** - 阿里云的AI服务
- **智谱AI** - 清华团队开发的ChatGLM
- **Moonshot AI(Kimi)** - 月之暗面的长文本模型
- **MiniMax** - 国产大模型厂商
- **OpenAI** - 国外GPT系列模型
- **自定义API** - 支持其他兼容OpenAI格式的API

### 配置方法
1. 点击界面上的"🔧 API设置"按钮
2. 从下拉菜单中选择AI模型提供商
3. 输入您的API密钥（Base URL和模型会自动填充）
4. 点击"🔗 测试连接"验证配置
5. 点击"💾 保存配置"保存设置

### 配置文件说明
- 配置信息保存在 `.env` 文件中
- 支持手动编辑配置文件
- 参考 `config_example.env` 文件了解配置格式

## 系统要求

### 编译版本
- Windows 10/11 (64位)
- SolidWorks 2020或更高版本
- 无需安装Python环境

### 源码版本
- Windows 10/11
- Python 3.8或更高版本
- SolidWorks 2020或更高版本
- 所需Python包已列在requirements.txt中

## 技术架构

### 模块化设计
项目采用现代化的模块化架构，符合以下编程原则：
- **SOLID原则**: 单一职责、开放封闭、里氏替换、接口隔离、依赖倒置
- **DRY原则**: 不重复自己
- **KISS原则**: 保持简单愚蠢
- **YAGNI原则**: 你不会需要它

### 核心模块
1. **配置管理**: 统一的配置文件处理和环境变量管理
2. **日志系统**: 分模块的日志记录和错误跟踪
3. **API客户端**: 统一的AI API通信接口
4. **脚本管理**: VBScript生成、执行、调试和历史管理
5. **UI组件**: 可复用的界面组件和对话框

## 故障排除

### 常见问题
1. **连接SolidWorks失败**
   - 确保SolidWorks已正确安装
   - 以管理员身份运行程序
   - 检查SolidWorks是否正在运行

2. **API调用失败**
   - 检查网络连接
   - 验证API密钥是否正确
   - 确认API服务商额度充足

3. **脚本执行错误**
   - 查看日志文件了解详细错误信息
   - 使用调试功能分析脚本问题
   - 参考scripts目录中的示例脚本

### 日志文件位置
程序运行日志保存在应用目录下的logs文件夹中。

## 支持与反馈

如果您在使用过程中遇到问题或有改进建议，欢迎通过以下方式联系：
- 提交GitHub Issue
- 使用应用内的反馈功能
- 查看调试帮助面板获取更多信息

## 许可证

本项目遵循相应的开源许可证，具体详情请查看LICENSE文件。

---

**享受智能化的SolidWorks体验！** 🚀 