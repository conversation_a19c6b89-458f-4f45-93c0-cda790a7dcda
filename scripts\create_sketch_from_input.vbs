Option Explicit

' 基于输入在SolidWorks中创建草图的脚本
' 此脚本连接到SolidWorks并根据输入参数创建草图
' 使用方法:
' cscript create_sketch_from_input.vbs shape=circle x=0 y=0 radius=10
' cscript create_sketch_from_input.vbs shape=rectangle x=0 y=0 width=20 height=10
' cscript create_sketch_from_input.vbs shape=line x1=0 y1=0 x2=30 y2=20

' 包含连接脚本
Dim fso, connectScriptPath, fileContents
Set fso = CreateObject("Scripting.FileSystemObject")
connectScriptPath = fso.GetParentFolderName(WScript.ScriptFullName) & "\connect_to_sw.vbs"

If fso.FileExists(connectScriptPath) Then
    ' 读取并包含连接文件
    Dim file
    Set file = fso.OpenTextFile(connectScriptPath, 1)
    fileContents = file.ReadAll()
    file.Close
    
    ' 执行连接脚本内容
    ExecuteGlobal fileContents
Else
    WScript.Echo "错误: 找不到connect_to_sw.vbs文件"
    WScript.Quit(1)
End If

' 解析参数的函数
Function ParseArguments()
    Dim params
    Set params = CreateObject("Scripting.Dictionary")
    Dim i, arg, argParts, key, value
    
    ' 检查参数数量
    If WScript.Arguments.Count < 1 Then
        WScript.Echo "错误: 提供的参数不足"
        WScript.Echo "使用方法:"
        WScript.Echo "cscript create_sketch_from_input.vbs shape=circle x=0 y=0 radius=10"
        WScript.Echo "cscript create_sketch_from_input.vbs shape=rectangle x=0 y=0 width=20 height=10"
        WScript.Echo "cscript create_sketch_from_input.vbs shape=line x1=0 y1=0 x2=30 y2=20"
        WScript.Quit(1)
    End If
    
    ' 处理所有参数
    For i = 0 To WScript.Arguments.Count - 1
        arg = WScript.Arguments(i)
        argParts = Split(arg, "=")
        
        If UBound(argParts) = 1 Then
            key = LCase(Trim(argParts(0)))
            value = Trim(argParts(1))
            
            ' 将数值转换为数字
            If IsNumeric(value) Then
                value = CDbl(value)
            End If
            
            params.Add key, value
        End If
    Next
    
    Set ParseArguments = params
End Function

' 主要变量
Dim swApp, swModel, swSketchManager

' 连接到SolidWorks
Set swApp = ConnectToSolidWorks()

' 创建或打开文档
On Error Resume Next
WScript.Echo "正在打开或创建新文档..."
Dim documentType
documentType = "Part"  ' 文档类型: Part, Assembly, Drawing

' 模板路径
Dim templatePath
templatePath = ""

' 如果有文档打开，则使用它
Set swModel = swApp.ActiveDoc

If swModel Is Nothing Then
    ' 检查常用模板路径
    If templatePath = "" Then
        templatePath = swApp.GetUserPreferenceStringValue(9)
    End If

    ' 如果找不到模板，使用默认路径
    If templatePath = "" Then
        If documentType = "Part" Then
            templatePath = swApp.GetUserPreferenceStringValue(0)
        ElseIf documentType = "Assembly" Then
            templatePath = swApp.GetUserPreferenceStringValue(1)
        ElseIf documentType = "Drawing" Then
            templatePath = swApp.GetUserPreferenceStringValue(2)
        End If
    End If

    ' 如果仍找不到模板，使用系统路径
    If templatePath = "" Then
        Dim templateFolder
        templateFolder = swApp.GetExecutablePath
        templateFolder = Left(templateFolder, InStrRev(templateFolder, "\"))
        templateFolder = templateFolder & "data\templates\"
        
        If documentType = "Part" Then
            templatePath = templateFolder & "Part.prtdot"
        ElseIf documentType = "Assembly" Then
            templatePath = templateFolder & "Assembly.asmdot"
        ElseIf documentType = "Drawing" Then
            templatePath = templateFolder & "Drawing.drwdot"
        End If
    End If

    WScript.Echo "使用模板: " & templatePath
    
    ' 创建新文档
    Set swModel = swApp.NewDocument(templatePath, 0, 0, 0)
    
    If Err.Number <> 0 Then
        WScript.Echo "创建新文档时出错: " & Err.Description
        WScript.Quit(1)
    End If
    
    If swModel Is Nothing Then
        WScript.Echo "错误: 模型未创建。"
        WScript.Quit(1)
    End If
Else
    WScript.Echo "使用现有活动文档..."
End If

On Error GoTo 0

' 显示俯视图
swModel.ShowNamedView2 "Top", 1

' 在上视基准面创建草图
Set swSketchManager = swModel.SketchManager
swSketchManager.InsertSketch True

' 获取和处理参数
Dim params, shape, x, y, radius, width, height, x1, y1, x2, y2
Set params = ParseArguments()

' 提取形状类型
If params.Exists("shape") Then
    shape = LCase(params("shape"))
Else
    WScript.Echo "错误: 形状类型(shape)未指定"
    WScript.Quit(1)
End If

' 根据形状类型处理
WScript.Echo "正在创建形状为 " & shape & " 的草图"
Select Case shape
    Case "circle"
        ' 圆形参数
        If params.Exists("x") Then x = params("x") Else x = 0
        If params.Exists("y") Then y = params("y") Else y = 0
        If params.Exists("radius") Then 
            radius = params("radius") / 1000  ' 转换为米
        Else
            radius = 0.01  ' 默认值 10mm
        End If
        
        ' 绘制圆形
        WScript.Echo "创建圆心为 (" & x & ", " & y & ") 半径为 " & radius * 1000 & "mm 的圆形"
        swSketchManager.CreateCircleByRadius x/1000, y/1000, 0, radius
        
    Case "rectangle"
        ' 矩形参数
        If params.Exists("x") Then x = params("x") Else x = 0
        If params.Exists("y") Then y = params("y") Else y = 0
        If params.Exists("width") Then 
            width = params("width") / 1000  ' 转换为米
        Else
            width = 0.02  ' 默认值 20mm
        End If
        If params.Exists("height") Then 
            height = params("height") / 1000  ' 转换为米
        Else
            height = 0.01  ' 默认值 10mm
        End If
        
        ' 绘制矩形
        WScript.Echo "创建中心为 (" & x & ", " & y & "), 宽度 " & width * 1000 & "mm 高度 " & height * 1000 & "mm 的矩形"
        swSketchManager.CreateCenterRectangle x/1000, y/1000, 0, (x/1000) + (width/2), (y/1000) + (height/2), 0
        
    Case "line"
        ' 直线参数
        If params.Exists("x1") Then x1 = params("x1") Else x1 = 0
        If params.Exists("y1") Then y1 = params("y1") Else y1 = 0
        If params.Exists("x2") Then x2 = params("x2") Else x2 = 0.03  ' 30mm
        If params.Exists("y2") Then y2 = params("y2") Else y2 = 0.02  ' 20mm
        
        ' 绘制直线
        WScript.Echo "创建从点 (" & x1 & ", " & y1 & ") 到点 (" & x2 & ", " & y2 & ") 的直线"
        swSketchManager.CreateLine x1/1000, y1/1000, 0, x2/1000, y2/1000, 0
        
    Case Else
        WScript.Echo "错误: 不支持的形状类型 '" & shape & "'"
        WScript.Echo "支持的形状: circle, rectangle, line"
        WScript.Quit(1)
End Select

' 结束草图
swSketchManager.InsertSketch True

' 显示等轴测视图
swModel.ShowNamedView2 "*Isometric", 7
swModel.ViewZoomtofit2

WScript.Echo "SUCCESS: 草图创建成功"
WScript.Quit(0) 