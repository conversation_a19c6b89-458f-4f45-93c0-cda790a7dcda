#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具管理器
管理所有快捷建模工具和便捷操作
"""

import os
import subprocess
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass

from config import config_manager
from logger import app_logger


@dataclass
class ToolInfo:
    """工具信息"""
    name: str
    description: str
    icon: str
    script_path: str
    category: str


class ToolsManager:
    """工具管理器 - 管理所有工具相关操作"""
    
    def __init__(self):
        self.config = config_manager.config
        self.tools_dir = os.path.join(self.config.scripts_dir, "tools")
        self.tools_registry = {}
        
        # 确保工具目录存在
        os.makedirs(self.tools_dir, exist_ok=True)
        
        # 初始化工具注册表
        self._initialize_tools_registry()
    
    def _initialize_tools_registry(self):
        """初始化工具注册表"""
        self.tools_registry = {
            # 基础几何体
            "create_box": ToolInfo(
                name="立方体",
                description="创建立方体/长方体",
                icon="📦",
                script_path=os.path.join(self.tools_dir, "create_box.vbs"),
                category="基础几何体"
            ),
            "create_cylinder": ToolInfo(
                name="圆柱体", 
                description="创建圆柱体",
                icon="🥫",
                script_path=os.path.join(self.tools_dir, "create_cylinder.vbs"),
                category="基础几何体"
            ),
            "create_sphere": ToolInfo(
                name="球体",
                description="创建球体",
                icon="⚽",
                script_path=os.path.join(self.tools_dir, "create_sphere.vbs"),
                category="基础几何体"
            ),
            "create_cone": ToolInfo(
                name="圆锥体",
                description="创建圆锥体", 
                icon="🔺",
                script_path=os.path.join(self.tools_dir, "create_cone.vbs"),
                category="基础几何体"
            ),
            
            # 机械零件
            "create_spur_gear": ToolInfo(
                name="直齿圆柱齿轮",
                description="创建标准直齿圆柱齿轮",
                icon="⚙️",
                script_path=os.path.join(self.tools_dir, "create_spur_gear.vbs"),
                category="机械零件"
            ),
            "create_bolt": ToolInfo(
                name="螺栓",
                description="创建标准螺栓",
                icon="🔩", 
                script_path=os.path.join(self.tools_dir, "create_bolt.vbs"),
                category="机械零件"
            ),
            "create_nut": ToolInfo(
                name="螺母",
                description="创建标准螺母",
                icon="🔧",
                script_path=os.path.join(self.tools_dir, "create_nut.vbs"),
                category="机械零件"
            ),
            "create_spring": ToolInfo(
                name="弹簧",
                description="创建螺旋弹簧",
                icon="🌀",
                script_path=os.path.join(self.tools_dir, "create_spring.vbs"),
                category="机械零件"
            ),
            
            # 便捷工具
            "measurement_tool": ToolInfo(
                name="测量工具",
                description="快速测量距离、角度、面积",
                icon="📏",
                script_path=os.path.join(self.tools_dir, "measurement_tool.vbs"),
                category="便捷工具"
            ),
            "material_tool": ToolInfo(
                name="材料设置",
                description="快速设置常用材料属性",
                icon="🧱",
                script_path=os.path.join(self.tools_dir, "material_tool.vbs"),
                category="便捷工具"
            ),
            "view_tool": ToolInfo(
                name="视图工具",
                description="快速切换标准视图",
                icon="👁️",
                script_path=os.path.join(self.tools_dir, "view_tool.vbs"),
                category="便捷工具"
            ),
            "export_tool": ToolInfo(
                name="导出工具",
                description="批量导出为常用格式",
                icon="📤",
                script_path=os.path.join(self.tools_dir, "export_tool.vbs"),
                category="便捷工具"
            )
        }
    
    def get_tools_by_category(self) -> Dict[str, List[ToolInfo]]:
        """按分类获取工具列表"""
        categories = {}
        
        for tool_id, tool_info in self.tools_registry.items():
            category = tool_info.category
            if category not in categories:
                categories[category] = []
            categories[category].append(tool_info)
        
        return categories
    
    def get_tool_info(self, tool_id: str) -> Optional[ToolInfo]:
        """获取工具信息"""
        return self.tools_registry.get(tool_id)
    
    def execute_tool(self, tool_id: str, tool_name: str = None) -> Tuple[bool, str, str]:
        """执行工具
        
        Args:
            tool_id: 工具ID
            tool_name: 工具名称（用于日志）
            
        Returns:
            (成功状态, 消息, 输出): 工具执行状态、消息和输出
        """
        try:
            tool_info = self.get_tool_info(tool_id)
            if not tool_info:
                return False, f"未找到工具: {tool_id}", ""
            
            script_path = tool_info.script_path
            display_name = tool_name or tool_info.name
            
            # 检查脚本文件是否存在
            if not os.path.exists(script_path):
                return False, f"工具脚本文件不存在: {script_path}", ""
            
            app_logger.main_logger.info(f"执行工具: {display_name} ({tool_id})")
            
            # 执行VBScript
            try:
                # 使用cscript执行VBScript
                result = subprocess.run(
                    ["cscript", "//NoLogo", script_path],
                    capture_output=True,
                    text=True,
                    encoding='gbk',
                    errors='replace',
                    timeout=300  # 5分钟超时
                )
                
                output = result.stdout
                error_output = result.stderr
                
                if result.returncode == 0:
                    app_logger.main_logger.info(f"工具 {display_name} 执行成功")
                    return True, f"工具 {display_name} 执行成功", output
                else:
                    error_msg = f"工具执行失败 (返回码: {result.returncode})"
                    if error_output:
                        error_msg += f"\n错误信息: {error_output}"
                    app_logger.main_logger.error(f"工具 {display_name} 执行失败: {error_msg}")
                    return False, error_msg, output
                    
            except subprocess.TimeoutExpired:
                error_msg = "工具执行超时"
                app_logger.main_logger.error(f"工具 {display_name} 执行超时")
                return False, error_msg, ""
                
            except Exception as e:
                error_msg = f"执行工具时出错: {str(e)}"
                app_logger.log_error(e, f"执行工具 {display_name}")
                return False, error_msg, ""
                
        except Exception as e:
            app_logger.log_error(e, f"工具管理器执行工具 {tool_id}")
            return False, f"工具管理器执行工具时出错: {str(e)}", ""
    
    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        available_tools = []
        
        for tool_id, tool_info in self.tools_registry.items():
            if os.path.exists(tool_info.script_path):
                available_tools.append(tool_id)
        
        return available_tools
    
    def validate_tools(self) -> Dict[str, bool]:
        """验证所有工具的可用性"""
        validation_results = {}
        
        for tool_id, tool_info in self.tools_registry.items():
            validation_results[tool_id] = os.path.exists(tool_info.script_path)
        
        return validation_results
    
    def get_missing_tools(self) -> List[str]:
        """获取缺失的工具列表"""
        missing_tools = []
        
        for tool_id, tool_info in self.tools_registry.items():
            if not os.path.exists(tool_info.script_path):
                missing_tools.append(tool_id)
        
        return missing_tools


# 创建全局工具管理器实例
tools_manager = ToolsManager()
