#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具模块测试脚本
测试工具管理器和工具面板的基本功能
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from tools_manager import tools_manager

def test_tools_manager():
    """测试工具管理器"""
    print("=== 测试工具管理器 ===")
    
    # 测试获取工具分类
    categories = tools_manager.get_tools_by_category()
    print(f"工具分类数量: {len(categories)}")
    
    for category_name, tools in categories.items():
        print(f"\n分类: {category_name}")
        for tool in tools:
            print(f"  - {tool.name} ({tool.icon}): {tool.description}")
    
    # 测试获取可用工具
    available_tools = tools_manager.get_available_tools()
    print(f"\n可用工具数量: {len(available_tools)}")
    print("可用工具:", available_tools)
    
    # 测试验证工具
    validation_results = tools_manager.validate_tools()
    print(f"\n工具验证结果:")
    for tool_id, is_valid in validation_results.items():
        status = "✓" if is_valid else "✗"
        print(f"  {status} {tool_id}")
    
    # 测试获取缺失工具
    missing_tools = tools_manager.get_missing_tools()
    if missing_tools:
        print(f"\n缺失的工具: {missing_tools}")
    else:
        print("\n所有工具脚本都存在")

def test_tool_info():
    """测试工具信息获取"""
    print("\n=== 测试工具信息 ===")
    
    # 测试获取特定工具信息
    test_tools = ["create_box", "create_cylinder", "measurement_tool"]
    
    for tool_id in test_tools:
        tool_info = tools_manager.get_tool_info(tool_id)
        if tool_info:
            print(f"\n工具: {tool_id}")
            print(f"  名称: {tool_info.name}")
            print(f"  描述: {tool_info.description}")
            print(f"  图标: {tool_info.icon}")
            print(f"  分类: {tool_info.category}")
            print(f"  脚本路径: {tool_info.script_path}")
            print(f"  脚本存在: {os.path.exists(tool_info.script_path)}")
        else:
            print(f"工具 {tool_id} 不存在")

def main():
    """主测试函数"""
    print("开始测试工具模块...")
    
    try:
        test_tools_manager()
        test_tool_info()
        
        print("\n=== 测试完成 ===")
        print("工具模块基本功能正常！")
        
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
