#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
管理应用程序的所有配置项
"""

import os
import logging
from typing import Optional, Dict, Any
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class APIConfig:
    """API配置"""
    api_key: str = ""
    base_url: str = "https://api.deepseek.com/v1/chat/completions"
    model: str = "deepseek-coder"
    provider: str = "deepseek"  # 新增：模型提供商
    timeout: int = 30
    
    def is_valid(self) -> bool:
        """检查API配置是否有效"""
        return bool(self.api_key and self.base_url and self.model)


# 国内大模型配置模板
MODEL_PROVIDERS = {
    "deepseek": {
        "name": "DeepSeek",
        "base_url": "https://api.deepseek.com/v1/chat/completions",
        "models": ["deepseek-coder", "deepseek-chat"],
        "default_model": "deepseek-coder"
    },
    "doubao": {
        "name": "豆包(字节跳动)",
        "base_url": "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
        "models": ["ep-20241230114937-lbxgk", "ep-20241230114725-d8qxz"],
        "default_model": "ep-20241230114937-lbxgk"
    },
    "wenxin": {
        "name": "文心一言(百度)",
        "base_url": "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions",
        "models": ["ernie-4.0-8k", "ernie-3.5-8k", "ernie-turbo-8k"],
        "default_model": "ernie-4.0-8k"
    },
    "tongyi": {
        "name": "通义千问(阿里)",
        "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions",
        "models": ["qwen-turbo", "qwen-plus", "qwen-max"],
        "default_model": "qwen-turbo"
    },
    "zhipu": {
        "name": "智谱AI",
        "base_url": "https://open.bigmodel.cn/api/paas/v4/chat/completions",
        "models": ["glm-4", "glm-4-air", "glm-3-turbo"],
        "default_model": "glm-4"
    },
    "moonshot": {
        "name": "Moonshot AI(Kimi)",
        "base_url": "https://api.moonshot.cn/v1/chat/completions",
        "models": ["moonshot-v1-8k", "moonshot-v1-32k", "moonshot-v1-128k"],
        "default_model": "moonshot-v1-8k"
    },
    "minimax": {
        "name": "MiniMax",
        "base_url": "https://api.minimax.chat/v1/text/chatcompletion_v2",
        "models": ["abab6.5s-chat", "abab6.5-chat", "abab5.5-chat"],
        "default_model": "abab6.5s-chat"
    },
    "openai": {
        "name": "OpenAI",
        "base_url": "https://api.openai.com/v1/chat/completions",
        "models": ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"],
        "default_model": "gpt-3.5-turbo"
    },
    "custom": {
        "name": "自定义API",
        "base_url": "",
        "models": ["custom-model"],
        "default_model": "custom-model"
    }
}


@dataclass
class UIConfig:
    """UI配置"""
    # 深色主题颜色
    bg_color: str = "#1A1A2E"
    sidebar_color: str = "#16213E" 
    card_color: str = "#0F3460"
    accent_color: str = "#E94560"
    text_color: str = "#FFFFFF"
    secondary_text: str = "#B2B1B9"
    highlight_color: str = "#E94560"
    
    # 按钮颜色
    button_bg: str = "#6A2CB3"
    button_active_bg: str = "#8A42D8"
    button_pressed_bg: str = "#551D99"
    
    # 输入框颜色
    input_bg: str = "#1E2A4A"
    
    # 字体设置
    default_font: tuple = ("Microsoft YaHei", 10)
    header_font: tuple = ("Microsoft YaHei", 16, "bold")
    button_font: tuple = ("Microsoft YaHei", 10)


@dataclass 
class AppConfig:
    """应用程序主配置"""
    # 路径配置
    scripts_dir: str = field(default_factory=lambda: os.path.join(os.path.dirname(os.path.abspath(__file__)), "scripts"))
    history_dir: str = field(default_factory=lambda: os.path.join(os.path.dirname(os.path.abspath(__file__)), "scripts", "history"))
    
    # 历史记录配置
    max_history: int = 20
    
    # 窗口配置
    window_title: str = "SolidWorks AI 助手"
    window_geometry: str = "1100x700"
    window_min_size: tuple = (900, 650)
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "sw_api_panel.log"
    
    # API配置
    api: APIConfig = field(default_factory=APIConfig)
    
    # UI配置
    ui: UIConfig = field(default_factory=UIConfig)
    
    def __post_init__(self):
        """初始化后的处理"""
        # 确保目录存在
        os.makedirs(self.scripts_dir, exist_ok=True)
        os.makedirs(self.history_dir, exist_ok=True)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.config = AppConfig()
        self._load_config()
    
    def _load_config(self) -> None:
        """从配置文件加载配置"""
        try:
            # 从.env文件加载
            if os.path.exists(".env"):
                self._load_from_env()
            
            # 从doc.txt文件加载（向后兼容）
            if os.path.exists("doc.txt"):
                self._load_from_doc_txt()
                
        except Exception as e:
            logger.error(f"加载配置时出错: {e}")
    
    def _load_from_env(self) -> None:
        """从.env文件加载配置"""
        try:
            with open(".env", "r", encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip().strip('"\'')
                        
                        if key == "OPENAI_API_KEY":
                            self.config.api.api_key = value
                        elif key == "OPENAI_BASE_URL":
                            self.config.api.base_url = value
                        elif key == "OPENAI_MODEL":
                            self.config.api.model = value
                        elif key == "OPENAI_PROVIDER":
                            self.config.api.provider = value
                        elif key == "LOG_LEVEL":
                            self.config.log_level = value
                            
        except Exception as e:
            logger.error(f"从.env文件加载配置时出错: {e}")
    
    def _load_from_doc_txt(self) -> None:
        """从doc.txt文件加载配置（向后兼容）"""
        try:
            with open("doc.txt", "r", encoding='utf-8') as f:
                for line in f:
                    line = line.strip().lower()
                    if not line:
                        continue
                    
                    if "api key" in line and "=" in line:
                        parts = line.split("=", 1)
                        if len(parts) == 2:
                            self.config.api.api_key = parts[1].strip().strip('"\'')
                    elif "base_url" in line and "=" in line:
                        parts = line.split("=", 1)
                        if len(parts) == 2:
                            self.config.api.base_url = parts[1].strip().strip('"\'')
                    elif "model" in line and "=" in line:
                        parts = line.split("=", 1)
                        if len(parts) == 2:
                            self.config.api.model = parts[1].strip().strip('"\'')
                            
        except Exception as e:
            logger.error(f"从doc.txt文件加载配置时出错: {e}")
    
    def save_api_config(self, api_key: str, base_url: str, model: str, provider: str = "deepseek") -> None:
        """保存API配置到.env文件"""
        try:
            self.config.api.api_key = api_key
            self.config.api.base_url = base_url  
            self.config.api.model = model
            self.config.api.provider = provider
            
            # 写入.env文件
            env_content = f"""# SolidWorks AI 助手配置文件
OPENAI_API_KEY={api_key}
OPENAI_BASE_URL={base_url}
OPENAI_MODEL={model}
OPENAI_PROVIDER={provider}
LOG_LEVEL={self.config.log_level}
"""
            
            with open(".env", "w", encoding='utf-8') as f:
                f.write(env_content)
                
            logger.info("API配置已保存到.env文件")
            
        except Exception as e:
            logger.error(f"保存API配置时出错: {e}")
            raise
    
    def get_provider_config(self, provider: str) -> Dict[str, Any]:
        """获取指定提供商的配置"""
        return MODEL_PROVIDERS.get(provider, MODEL_PROVIDERS["deepseek"])
    
    def get_available_providers(self) -> Dict[str, str]:
        """获取可用的模型提供商列表"""
        return {key: config["name"] for key, config in MODEL_PROVIDERS.items()}
    
    def get_current_script_path(self) -> str:
        """获取当前脚本路径"""
        return os.path.join(self.config.scripts_dir, "current_script.vbs")
    
    def get_sample_script_path(self) -> str:
        """获取示例脚本路径"""
        return os.path.join(self.config.scripts_dir, "create_simple_part.vbs")
    
    def ensure_current_script_exists(self) -> None:
        """确保当前脚本存在，如果不存在则从示例复制"""
        current_path = self.get_current_script_path()
        sample_path = self.get_sample_script_path()
        
        if not os.path.exists(current_path) and os.path.exists(sample_path):
            import shutil
            shutil.copy2(sample_path, current_path)
            logger.info("示例脚本已复制为当前脚本")


# 全局配置实例
config_manager = ConfigManager() 