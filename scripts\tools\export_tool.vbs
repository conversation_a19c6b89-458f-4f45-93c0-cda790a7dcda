Option Explicit

' SolidWorks导出工具脚本
' 提供快速导出为常用格式功能
' 作者: SolidWorks AI助手

' 主要变量
Dim swApp, swModel, swModelDoc

' 连接到SolidWorks
On Error Resume Next
WScript.Echo "正在连接到SolidWorks..."
Set swApp = GetObject(, "SldWorks.Application")

If Err.Number <> 0 Then
    Err.Clear
    WScript.Echo "SolidWorks未运行，尝试启动..."
    Set swApp = CreateObject("SldWorks.Application")
    If Err.Number <> 0 Then
        WScript.Echo "连接SolidWorks时出错: " & Err.Description
        WScript.Quit(1)
    End If
End If

swApp.Visible = True
WScript.Echo "成功连接到SolidWorks。"
On Error Goto 0

' 获取当前活动文档
Set swModel = swApp.ActiveDoc
If swModel Is Nothing Then
    WScript.Echo "没有打开的文档。请先打开一个SolidWorks文档。"
    WScript.Quit(1)
End If

Set swModelDoc = swModel

' 获取当前文档路径和名称
Dim docPath, docName, docExt, docDir
docPath = swModelDoc.GetPathName()

If docPath = "" Then
    WScript.Echo "当前文档未保存。请先保存文档。"
    swApp.RunCommand 2, ""  ' 另存为命令
    docPath = swModelDoc.GetPathName()
    
    If docPath = "" Then
        WScript.Echo "文档未保存，无法继续。"
        WScript.Quit(1)
    End If
End If

' 解析文档路径
docDir = Left(docPath, InStrRev(docPath, "\"))
docName = Mid(docPath, InStrRev(docPath, "\") + 1)
docName = Left(docName, InStrRev(docName, ".") - 1)

' 显示导出选择菜单
Dim choice
choice = InputBox("请选择导出格式:" & vbCrLf & _
                 "1 - STEP (.step)" & vbCrLf & _
                 "2 - IGES (.igs)" & vbCrLf & _
                 "3 - STL (.stl)" & vbCrLf & _
                 "4 - 3D PDF (.pdf)" & vbCrLf & _
                 "5 - Parasolid (.x_t)" & vbCrLf & _
                 "6 - VRML (.wrl)" & vbCrLf & _
                 "7 - 3D XML (.3dxml)" & vbCrLf & _
                 "8 - 批量导出(多种格式)", "导出工具", "1")

If choice = "" Then WScript.Quit(0)

' 导出函数
Function ExportFile(format, extension)
    Dim exportPath
    exportPath = docDir & docName & extension
    
    WScript.Echo "正在导出为 " & format & " 格式..."
    WScript.Echo "导出路径: " & exportPath
    
    Dim exportOptions
    Set exportOptions = swModelDoc.ExportFileProperties
    
    ' 设置导出选项
    Select Case LCase(extension)
        Case ".step", ".stp"
            ' STEP导出选项
            exportOptions.SetApSchema 1  ' AP214
            exportOptions.SetExportSplitPeriodicFaces True
            
        Case ".stl"
            ' STL导出选项
            exportOptions.SetProperty 1, 0  ' 二进制格式
            exportOptions.SetProperty 2, 0.001  ' 精度
            
        Case ".3dxml"
            ' 3DXML导出选项
            exportOptions.SetProperty 1, 1  ' 包含PMI
    End Select
    
    ' 执行导出
    Dim result
    result = swModelDoc.Extension.SaveAs(exportPath, 0, 0, exportOptions, "", False, 0)
    
    If result Then
        WScript.Echo "导出成功: " & exportPath
        Return True
    Else
        WScript.Echo "导出失败"
        Return False
    End If
End Function

' 根据选择执行导出
Select Case CInt(choice)
    Case 1
        ' STEP
        ExportFile "STEP", ".step"
        
    Case 2
        ' IGES
        ExportFile "IGES", ".igs"
        
    Case 3
        ' STL
        ExportFile "STL", ".stl"
        
    Case 4
        ' 3D PDF
        ExportFile "3D PDF", ".pdf"
        
    Case 5
        ' Parasolid
        ExportFile "Parasolid", ".x_t"
        
    Case 6
        ' VRML
        ExportFile "VRML", ".wrl"
        
    Case 7
        ' 3D XML
        ExportFile "3D XML", ".3dxml"
        
    Case 8
        ' 批量导出
        WScript.Echo "正在批量导出多种格式..."
        
        Dim formats, extensions, i
        formats = Array("STEP", "STL", "IGES")
        extensions = Array(".step", ".stl", ".igs")
        
        Dim successCount
        successCount = 0
        
        For i = 0 To UBound(formats)
            If ExportFile(formats(i), extensions(i)) Then
                successCount = successCount + 1
            End If
        Next
        
        WScript.Echo "批量导出完成。成功: " & successCount & "/" & UBound(formats) + 1
        
    Case Else
        WScript.Echo "无效的选择。"
        WScript.Quit(1)
End Select

WScript.Echo "SUCCESS: 导出工具执行完成。"
WScript.Quit(0)
