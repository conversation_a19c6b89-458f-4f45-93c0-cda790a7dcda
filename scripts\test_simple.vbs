Option Explicit

Dim swApp
On Error Resume Next
WScript.Echo "Connecting to SolidWorks..."
Set swApp = GetObject(, "SldWorks.Application")
If Err.Number <> 0 Then
    Err.Clear
    Set swApp = CreateObject("SldWorks.Application")
    If Err.Number <> 0 Then
        WScript.Echo "Error connecting to SolidWorks"
        WScript.Quit(1)
    End If
End If
swApp.Visible = True
WScript.Echo "Successfully connected to SolidWorks"

Dim swModel
Set swModel = swApp.ActiveDoc
If swModel Is Nothing Then
    WScript.Echo "No active document. Please open or create a part document in SolidWorks first."
    WScript.Quit(1)
End If

WScript.Echo "Found active document: " & swModel.GetTitle()

Dim swSketchMgr
Set swSketchMgr = swModel.SketchManager

WScript.Echo "Clearing selection..."
swModel.ClearSelection2 True

WScript.Echo "Attempting to select Front Plane..."
Dim bRet
bRet = swModel.Extension.SelectByID2("Front Plane", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
WScript.Echo "Front Plane selection result: " & bRet

If bRet = False Then
    WScript.Echo "Attempting to select Top Plane..."
    bRet = swModel.Extension.SelectByID2("Top Plane", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
    WScript.Echo "Top Plane selection result: " & bRet
End If

If bRet = False Then
    WScript.Echo "Attempting to select Right Plane..."
    bRet = swModel.Extension.SelectByID2("Right Plane", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
    WScript.Echo "Right Plane selection result: " & bRet
End If

If bRet = True Then
    WScript.Echo "Creating sketch..."
    swSketchMgr.InsertSketch True
    
    WScript.Echo "Drawing circle..."
    swSketchMgr.CreateCircle 0, 0, 0, 0.025, 0, 0
    
    WScript.Echo "Exiting sketch..."
    swSketchMgr.InsertSketch True
    
    WScript.Echo "SUCCESS: Circle created"
Else
    WScript.Echo "ERROR: Could not select any datum plane"
End If

WScript.Quit(0)
