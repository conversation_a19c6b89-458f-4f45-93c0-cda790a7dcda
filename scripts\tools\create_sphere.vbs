Option Explicit

' 创建球体的SolidWorks脚本
' 此脚本创建一个参数化的球体
' 作者: SolidWorks AI助手

' 主要变量
Dim swApp, swModel, swModelDoc, swSketchManager, swFeatureManager
Dim radius

' 获取用户输入的尺寸参数
radius = InputBox("请输入球体半径 (mm):", "球体参数", "25")
If radius = "" Then WScript.Quit(0)
radius = CDbl(radius) / 1000  ' 转换为米

' 连接到SolidWorks
On Error Resume Next
WScript.Echo "正在连接到SolidWorks..."
Set swApp = GetObject(, "SldWorks.Application")

If Err.Number <> 0 Then
    Err.Clear
    WScript.Echo "SolidWorks未运行，尝试启动..."
    Set swApp = CreateObject("SldWorks.Application")
    If Err.Number <> 0 Then
        WScript.Echo "连接SolidWorks时出错: " & Err.Description
        WScript.Quit(1)
    End If
End If

swApp.Visible = True
WScript.Echo "成功连接到SolidWorks。"
On Error Goto 0

' 创建新零件文档
On Error Resume Next
WScript.Echo "正在创建新零件文档..."

Set swModel = swApp.NewPart()
If swModel Is Nothing Then
    WScript.Echo "创建零件文档失败，尝试使用模板..."
    Set swModel = swApp.NewDocument("", 1, 0, 0)
End If

If Err.Number <> 0 Then
    WScript.Echo "创建零件文档时出错: " & Err.Description
    WScript.Quit(1)
End If

If swModel Is Nothing Then
    WScript.Echo "错误: 零件模型未创建。"
    WScript.Quit(1)
End If

Set swModelDoc = swModel
Set swSketchManager = swModelDoc.SketchManager
Set swFeatureManager = swModelDoc.FeatureManager

WScript.Echo "新零件文档创建成功。"
On Error Goto 0

' 创建球体
WScript.Echo "正在创建球体..."

' 方法1: 使用旋转特征创建球体
On Error Resume Next

' 选择前视基准面
Dim boolStatus
boolStatus = swModelDoc.Extension.SelectByID2("Front Plane", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
If Not boolStatus Then
    boolStatus = swModelDoc.Extension.SelectByID2("前视基准面", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
End If

If Not boolStatus Then
    WScript.Echo "选择基准面失败，尝试其他方法..."
    ' 尝试方法2: 使用特征菜单创建球体
    swApp.RunCommand 1599, ""  ' 创建球体特征
    WScript.Quit(0)
End If

' 创建草图
swSketchManager.InsertSketch True

' 绘制半圆
swSketchManager.CreateCircleByRadius 0, 0, 0, radius
WScript.Echo "创建了半径为 " & (radius*1000) & " mm的圆形。"

' 添加中心线
swSketchManager.CreateCenterLine 0, -radius*1.5, 0, 0, radius*1.5, 0

' 结束草图
swSketchManager.InsertSketch True

' 选择草图
swModelDoc.ClearSelection2 True
boolStatus = swModelDoc.Extension.SelectByID2("Sketch1", "SKETCH", 0, 0, 0, False, 0, Nothing, 0)

If boolStatus Then
    ' 创建旋转特征
    Dim myFeature
    Set myFeature = swFeatureManager.FeatureRevolve2(True, True, False, False, False, False, 0, 0, 6.28318530718, 0, False, False, False, False, False, False, False, False, False, 0, 0, False)
    
    If myFeature Is Nothing Then
        WScript.Echo "旋转特征创建失败，尝试其他方法..."
        ' 使用菜单命令
        swApp.RunCommand 1599, ""  ' 创建球体特征
    Else
        WScript.Echo "球体创建成功。"
    End If
Else
    WScript.Echo "选择草图失败"
End If

' 显示等轴测视图并缩放到合适大小
swModelDoc.ShowNamedView2 "*Isometric", 7
swModelDoc.ViewZoomtofit2

' 重命名特征
On Error Resume Next
swModelDoc.Extension.SelectByID2 "Revolve1", "BODYFEATURE", 0, 0, 0, False, 0, Nothing, 0
swModelDoc.SelectedFeatureProperties 0, 0, 0, 0, 0, 0, 0, True, False, "球体"
On Error Goto 0

WScript.Echo "SUCCESS: 球体创建完成！"
WScript.Echo "尺寸: 半径 " & (radius*1000) & " mm"
WScript.Quit(0)
