Option Explicit

' 在SolidWorks中创建草图的脚本
' 此脚本连接到SolidWorks并创建用户请求形状的草图

' 包含连接脚本
' 注意：请放置在脚本路径中
Dim fso, connectScriptPath, fileContents
Set fso = CreateObject("Scripting.FileSystemObject")
connectScriptPath = fso.GetParentFolderName(WScript.ScriptFullName) & "\connect_to_sw.vbs"

If fso.FileExists(connectScriptPath) Then
    ' 读取并包含连接文件
    Dim file
    Set file = fso.OpenTextFile(connectScriptPath, 1)
    fileContents = file.ReadAll()
    file.Close
    
    ' 执行连接脚本内容
    ExecuteGlobal fileContents
Else
    WScript.Echo "错误: 找不到connect_to_sw.vbs文件"
    WScript.Quit(1)
End If

' 主要变量
Dim swApp, swModel, swSketch, swSketchManager

' 连接到SolidWorks
Set swApp = ConnectToSolidWorks()

' 创建或打开文档
On Error Resume Next
WScript.Echo "正在打开或创建新文档..."
Dim documentType
documentType = "Part"  ' 文档类型: Part, Assembly, Drawing

' 模板路径
Dim templatePath
templatePath = ""

' 如果有文档打开，则使用它
Set swModel = swApp.ActiveDoc

If swModel Is Nothing Then
    ' 检查常用模板路径
    If templatePath = "" Then
        templatePath = swApp.GetUserPreferenceStringValue(9)
    End If

    ' 如果找不到模板，使用默认路径
    If templatePath = "" Then
        If documentType = "Part" Then
            templatePath = swApp.GetUserPreferenceStringValue(0)
        ElseIf documentType = "Assembly" Then
            templatePath = swApp.GetUserPreferenceStringValue(1)
        ElseIf documentType = "Drawing" Then
            templatePath = swApp.GetUserPreferenceStringValue(2)
        End If
    End If

    ' 如果仍找不到模板，使用系统路径
    If templatePath = "" Then
        Dim templateFolder
        templateFolder = swApp.GetExecutablePath
        templateFolder = Left(templateFolder, InStrRev(templateFolder, "\"))
        templateFolder = templateFolder & "data\templates\"
        
        If documentType = "Part" Then
            templatePath = templateFolder & "Part.prtdot"
        ElseIf documentType = "Assembly" Then
            templatePath = templateFolder & "Assembly.asmdot"
        ElseIf documentType = "Drawing" Then
            templatePath = templateFolder & "Drawing.drwdot"
        End If
    End If

    WScript.Echo "使用模板: " & templatePath
    
    ' 创建新文档
    Set swModel = swApp.NewDocument(templatePath, 0, 0, 0)
    
    If Err.Number <> 0 Then
        WScript.Echo "创建新文档时出错: " & Err.Description
        WScript.Quit(1)
    End If
    
    If swModel Is Nothing Then
        WScript.Echo "错误: 模型未创建。"
        WScript.Quit(1)
    End If
Else
    WScript.Echo "使用现有活动文档..."
End If

On Error GoTo 0

' 显示俯视图
swModel.ShowNamedView2 "Top", 1

' 在上视基准面创建草图
Set swSketchManager = swModel.SketchManager
swSketchManager.InsertSketch True

' 在草图中创建形状
' 示例：绘制一个矩形
WScript.Echo "正在创建草图..."
swSketchManager.CreateCenterRectangle 0, 0, 0, 0.05, 0.05, 0

' 示例：绘制一个圆
swSketchManager.CreateCircleByRadius 0, 0, 0, 0.025

' 示例：绘制一条线
swSketchManager.CreateLine -0.05, -0.05, 0, 0.05, -0.05, 0

' 结束草图
swSketchManager.InsertSketch True

' 显示等轴测视图
swModel.ShowNamedView2 "*Isometric", 7
swModel.ViewZoomtofit2

WScript.Echo "SUCCESS: 草图创建成功"
WScript.Quit(0) 