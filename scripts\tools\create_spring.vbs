Option Explicit

' 创建螺旋弹簧的SolidWorks脚本
' 此脚本创建一个简化的螺旋弹簧
' 作者: SolidWorks AI助手

' 主要变量
Dim swApp, swModel, swModelDoc, swSketchManager, swFeatureManager
Dim wireDiameter, springDiameter, springLength, coils

' 获取用户输入的弹簧参数
wireDiameter = InputBox("请输入弹簧丝直径 (mm):", "弹簧参数", "2")
If wireDiameter = "" Then WScript.Quit(0)
wireDiameter = CDbl(wireDiameter) / 1000  ' 转换为米

springDiameter = InputBox("请输入弹簧外径 (mm):", "弹簧参数", "20")
If springDiameter = "" Then WScript.Quit(0)
springDiameter = CDbl(springDiameter) / 1000  ' 转换为米

springLength = InputBox("请输入弹簧长度 (mm):", "弹簧参数", "50")
If springLength = "" Then WScript.Quit(0)
springLength = CDbl(springLength) / 1000  ' 转换为米

coils = InputBox("请输入弹簧圈数:", "弹簧参数", "8")
If coils = "" Then WScript.Quit(0)
coils = CDbl(coils)

' 连接到SolidWorks
On Error Resume Next
WScript.Echo "正在连接到SolidWorks..."
Set swApp = GetObject(, "SldWorks.Application")

If Err.Number <> 0 Then
    Err.Clear
    WScript.Echo "SolidWorks未运行，尝试启动..."
    Set swApp = CreateObject("SldWorks.Application")
    If Err.Number <> 0 Then
        WScript.Echo "连接SolidWorks时出错: " & Err.Description
        WScript.Quit(1)
    End If
End If

swApp.Visible = True
WScript.Echo "成功连接到SolidWorks。"
On Error Goto 0

' 创建新零件文档
On Error Resume Next
WScript.Echo "正在创建新零件文档..."

Set swModel = swApp.NewPart()
If swModel Is Nothing Then
    Set swModel = swApp.NewDocument("", 1, 0, 0)
End If

If swModel Is Nothing Then
    WScript.Echo "错误: 零件模型未创建。"
    WScript.Quit(1)
End If

Set swModelDoc = swModel
Set swSketchManager = swModelDoc.SketchManager
Set swFeatureManager = swModelDoc.FeatureManager

WScript.Echo "新零件文档创建成功。"
On Error Goto 0

' 创建螺旋线路径
WScript.Echo "正在创建螺旋线路径..."

' 选择前视基准面
On Error Resume Next
Dim boolStatus
boolStatus = swModelDoc.Extension.SelectByID2("Front Plane", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
If Not boolStatus Then
    boolStatus = swModelDoc.Extension.SelectByID2("前视基准面", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
End If

If Not boolStatus Then
    WScript.Echo "选择基准面失败"
    WScript.Quit(1)
End If
On Error Goto 0

' 创建螺旋线基础圆
swSketchManager.InsertSketch True

Dim helixRadius
helixRadius = (springDiameter - wireDiameter) / 2
swSketchManager.CreateCircleByRadius 0, 0, 0, helixRadius

WScript.Echo "创建了螺旋线基础圆，半径: " & (helixRadius*1000) & " mm"

' 结束草图
swSketchManager.InsertSketch True

' 创建螺旋线特征
WScript.Echo "正在创建螺旋线..."

' 选择圆形草图
swModelDoc.ClearSelection2 True
boolStatus = swModelDoc.Extension.SelectByID2("Sketch1", "SKETCH", 0, 0, 0, False, 0, Nothing, 0)

If boolStatus Then
    ' 创建螺旋线
    Dim helixFeature
    Set helixFeature = swFeatureManager.InsertHelicalCurve5(True, True, True, 0, 0, springLength, springLength / coils, 0, True, 0)

    If helixFeature Is Nothing Then
        WScript.Echo "螺旋线创建失败，尝试简化方法..."
        ' 创建简化的弹簧（使用多个圆环）
        CreateSimplifiedSpring
    Else
        WScript.Echo "螺旋线创建成功"
        CreateSweepSpring
    End If
Else
    WScript.Echo "选择草图失败，创建简化弹簧..."
    CreateSimplifiedSpring
End If

' 显示等轴测视图并缩放到合适大小
swModelDoc.ShowNamedView2 "*Isometric", 7
swModelDoc.ViewZoomtofit2

WScript.Echo "SUCCESS: 弹簧创建完成！"
WScript.Echo "参数: 丝径" & (wireDiameter*1000) & "mm, 外径" & (springDiameter*1000) & "mm"
WScript.Echo "长度: " & (springLength*1000) & "mm, 圈数: " & coils
WScript.Quit(0)

' 创建扫描弹簧的子程序
Sub CreateSweepSpring()
    WScript.Echo "正在创建扫描弹簧..."

    ' 选择右视基准面创建截面
    swModelDoc.ClearSelection2 True
    boolStatus = swModelDoc.Extension.SelectByID2("Right Plane", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
    If Not boolStatus Then
        boolStatus = swModelDoc.Extension.SelectByID2("右视基准面", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
    End If

    If boolStatus Then
        ' 创建弹簧丝截面草图
        swSketchManager.InsertSketch True
        swSketchManager.CreateCircleByRadius helixRadius, 0, 0, wireDiameter / 2
        swSketchManager.InsertSketch True

        ' 执行扫描
        swModelDoc.ClearSelection2 True
        boolStatus = swModelDoc.Extension.SelectByID2("Sketch2", "SKETCH", 0, 0, 0, False, 0, Nothing, 0)
        boolStatus = swModelDoc.Extension.SelectByID2("Helix/Spiral1", "REFERENCECURVES", 0, 0, 0, True, 0, Nothing, 0)

        Dim sweepFeature
        Set sweepFeature = swFeatureManager.InsertProtrusionSwept2(False, False, 0, 0, 0, 0, 0, 0, True, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0)

        If sweepFeature Is Nothing Then
            WScript.Echo "扫描失败，创建简化弹簧..."
            CreateSimplifiedSpring
        Else
            WScript.Echo "扫描弹簧创建成功"
        End If
    End If
End Sub

' 创建简化弹簧的子程序
Sub CreateSimplifiedSpring()
    WScript.Echo "正在创建简化弹簧（圆环堆叠）..."

    Dim i, ringHeight, currentZ
    ringHeight = springLength / coils

    For i = 0 To coils - 1
        currentZ = i * ringHeight

        ' 选择与当前高度平行的基准面
        swModelDoc.ClearSelection2 True
        boolStatus = swModelDoc.Extension.SelectByID2("Front Plane", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
        If Not boolStatus Then
            boolStatus = swModelDoc.Extension.SelectByID2("前视基准面", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
        End If

        If boolStatus Then
            ' 创建圆环草图
            swSketchManager.InsertSketch True
            swSketchManager.CreateCircleByRadius 0, 0, 0, springDiameter / 2
            swSketchManager.CreateCircleByRadius 0, 0, 0, (springDiameter - 2 * wireDiameter) / 2
            swSketchManager.InsertSketch True

            ' 拉伸圆环
            swModelDoc.ClearSelection2 True
            Dim sketchName
            sketchName = "Sketch" & (i + 2)
            boolStatus = swModelDoc.Extension.SelectByID2(sketchName, "SKETCH", 0, 0, 0, False, 0, Nothing, 0)

            If boolStatus Then
                Dim ringFeature
                Set ringFeature = swFeatureManager.FeatureExtrusion2(True, False, False, 0, 0, ringHeight * 0.8, 0.01, False, 0, False, False, 0, 0, False, False, False, False, False, False, False, False, False, False)
            End If
        End If
    Next

    WScript.Echo "简化弹簧创建完成"
End Sub