#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI API客户端模块
处理与AI服务的所有交互
"""

import requests
from typing import Dict, List, Any, Optional, Tuple
import json
from dataclasses import dataclass

from config import config_manager
from logger import app_logger


@dataclass
class ChatMessage:
    """聊天消息"""
    role: str  # "system", "user", "assistant"
    content: str


@dataclass
class APIResponse:
    """API响应"""
    success: bool
    content: str = ""
    error: str = ""
    status_code: Optional[int] = None


class AIAPIClient:
    """AI API客户端"""
    
    def __init__(self):
        self.config = config_manager.config.api
        self._update_headers()
    
    def _update_headers(self) -> None:
        """更新请求头"""
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.config.api_key}",
            "HTTP-Referer": "https://chaovy.app"
        }
    
    def update_config(self, api_key: str, base_url: str, model: str, provider: str = None) -> None:
        """更新API配置"""
        self.config.api_key = api_key
        self.config.base_url = base_url
        self.config.model = model
        if provider:
            self.config.provider = provider
        self._update_headers()
        app_logger.log_config_change("API配置", "已更新")
    
    def test_connection(self) -> APIResponse:
        """测试API连接"""
        if not self.config.is_valid():
            return APIResponse(
                success=False,
                error="API配置不完整，请检查API密钥、地址和模型"
            )
        
        try:
            app_logger.log_api_request("POST", self.config.base_url)
            
            # 创建测试消息
            messages = [
                ChatMessage("system", "You are a helpful assistant."),
                ChatMessage("user", "Say 'Test connection successful' if you can read this.")
            ]
            
            payload = {
                "model": self.config.model,
                "messages": [{"role": msg.role, "content": msg.content} for msg in messages],
                "temperature": 0.2,
                "max_tokens": 20
            }
            
            response = requests.post(
                self.config.base_url,
                headers=self.headers,
                json=payload,
                timeout=self.config.timeout
            )
            
            app_logger.log_api_request("POST", self.config.base_url, response.status_code)
            
            if response.status_code == 200:
                data = response.json()
                if 'choices' in data and len(data['choices']) > 0:
                    content = data['choices'][0]['message']['content']
                    return APIResponse(
                        success=True,
                        content=content,
                        status_code=response.status_code
                    )
                else:
                    return APIResponse(
                        success=False,
                        error="响应格式不正确",
                        status_code=response.status_code
                    )
            else:
                error_msg = f"HTTP {response.status_code}"
                try:
                    error_data = response.json()
                    if 'error' in error_data:
                        if isinstance(error_data['error'], dict):
                            error_msg = error_data['error'].get('message', error_msg)
                        else:
                            error_msg = str(error_data['error'])
                except:
                    pass
                
                app_logger.log_api_error("POST", self.config.base_url, error_msg)
                return APIResponse(
                    success=False,
                    error=error_msg,
                    status_code=response.status_code
                )
                
        except requests.exceptions.Timeout:
            error = "请求超时，请检查网络连接"
            app_logger.log_api_error("POST", self.config.base_url, error)
            return APIResponse(success=False, error=error)
            
        except requests.exceptions.ConnectionError:
            error = "连接失败，请检查网络和API地址"
            app_logger.log_api_error("POST", self.config.base_url, error)
            return APIResponse(success=False, error=error)
            
        except Exception as e:
            error = f"未知错误: {str(e)}"
            app_logger.log_api_error("POST", self.config.base_url, error)
            return APIResponse(success=False, error=error)
    
    def chat_completion(self, messages: List[ChatMessage], temperature: float = 0.7, max_tokens: int = 2000) -> APIResponse:
        """进行聊天完成请求"""
        if not self.config.is_valid():
            return APIResponse(
                success=False,
                error="API配置不完整，请检查API密钥、地址和模型"
            )
        
        try:
            app_logger.log_api_request("POST", self.config.base_url)
            
            payload = {
                "model": self.config.model,
                "messages": [{"role": msg.role, "content": msg.content} for msg in messages],
                "temperature": temperature,
                "max_tokens": max_tokens
            }
            
            response = requests.post(
                self.config.base_url,
                headers=self.headers,
                json=payload,
                timeout=self.config.timeout
            )
            
            app_logger.log_api_request("POST", self.config.base_url, response.status_code)
            
            if response.status_code == 200:
                data = response.json()
                if 'choices' in data and len(data['choices']) > 0:
                    content = data['choices'][0]['message']['content']
                    return APIResponse(
                        success=True,
                        content=content,
                        status_code=response.status_code
                    )
                else:
                    return APIResponse(
                        success=False,
                        error="响应格式不正确",
                        status_code=response.status_code
                    )
            else:
                error_msg = f"HTTP {response.status_code}"
                try:
                    error_data = response.json()
                    if 'error' in error_data:
                        if isinstance(error_data['error'], dict):
                            error_msg = error_data['error'].get('message', error_msg)
                        else:
                            error_msg = str(error_data['error'])
                except:
                    pass
                
                app_logger.log_api_error("POST", self.config.base_url, error_msg)
                return APIResponse(
                    success=False,
                    error=error_msg,
                    status_code=response.status_code
                )
                
        except requests.exceptions.Timeout:
            error = "请求超时，请检查网络连接"
            app_logger.log_api_error("POST", self.config.base_url, error)
            return APIResponse(success=False, error=error)
            
        except requests.exceptions.ConnectionError:
            error = "连接失败，请检查网络和API地址"
            app_logger.log_api_error("POST", self.config.base_url, error)
            return APIResponse(success=False, error=error)
            
        except Exception as e:
            error = f"未知错误: {str(e)}"
            app_logger.log_api_error("POST", self.config.base_url, error)
            return APIResponse(success=False, error=error)


class APIService:
    """API服务，提供高级功能"""
    
    def __init__(self):
        self.client = AIAPIClient()
    
    def test_api_connection(self) -> Tuple[bool, str]:
        """测试API连接（兼容性方法）"""
        response = self.client.test_connection()
        return response.success, response.content if response.success else response.error
    
    def generate_script(self, query: str) -> Tuple[bool, str]:
        """生成VBScript脚本"""
        system_prompt = """You are an expert in SolidWorks automation with VBScript. 
Your task is to generate VBScript code that can automate SolidWorks operations.
You MUST understand user instructions in both English and Chinese language.
Keep your responses focused only on the VBScript code without any explanations.

CRITICAL: Use ONLY English text in all WScript.Echo statements and comments. 
Never use Chinese characters in the generated VBScript code to avoid encoding issues.

IMPORTANT: Always structure your script in this sequence:
1. Start with 'Option Explicit'
2. Include connection code that connects to SolidWorks first:

Dim swApp
On Error Resume Next
WScript.Echo "Connecting to SolidWorks..."
Set swApp = GetObject(, "SldWorks.Application")
If Err.Number <> 0 Then
    Err.Clear
    WScript.Echo "SolidWorks not running, trying to start..."
    Set swApp = CreateObject("SldWorks.Application")
    If Err.Number <> 0 Then
        WScript.Echo "Error connecting to SolidWorks: " & Err.Description
        WScript.Quit(1)
    End If
End If
swApp.Visible = True
WScript.Echo "Successfully connected to SolidWorks."
On Error Goto 0

3. Create or open a document using swApp.NewPart() (do NOT use hard-coded template paths)
4. Implement the requested feature or operation
5. Include proper error handling

The script must run independently and include all necessary code to connect to SolidWorks, 
not relying on any external functions or files.
Always end with a success message and return 0 exit code on success.
Use ONLY English in echo statements and comments.

IMPORTANT RULES:
- Use swApp.NewPart() to create new part documents (do not use hard-coded template paths)
- Always check if objects are Nothing before using them
- Use proper VBScript syntax and variable declarations
- Include meaningful error messages in English only

CRITICAL METHOD SIGNATURES - USE THESE EXACTLY:
- CreateCircle: swSketchMgr.CreateCircle(centerX, centerY, centerZ, edgeX, edgeY, edgeZ)
- CreateLine: swSketchMgr.CreateLine(startX, startY, startZ, endX, endY, endZ)
- CreateCornerRectangle: swSketchMgr.CreateCornerRectangle(x1, y1, z1, x2, y2, z2)
- InsertSketch: swSketchMgr.InsertSketch True (do NOT assign to variable - does not return object)
- FeatureExtrusion2: swFeatMgr.FeatureExtrusion2(True, False, False, 0, 0, distance, 0.01, False, False, False, False, 0, 0, False, False, False, False, True, True, True, 0, 0, False)

SKETCH WORKFLOW - FOLLOW THIS EXACT SEQUENCE:
1. Select datum plane using the provided plane selection code
2. Call swSketchMgr.InsertSketch True to start sketch
3. Create sketch entities (circles, lines, etc.)
4. Call swSketchMgr.InsertSketch True again to exit sketch
5. Create features (extrusion, etc.) - no need to manually select sketch

ALWAYS SELECT DATUM PLANE BEFORE SKETCH:
Use this code block to find and select first datum plane:
Dim swFeat, bFound
Set swFeat = swModel.FirstFeature()
bFound = False
Do While Not swFeat Is Nothing And bFound = False
    If swFeat.GetTypeName() = "RefPlane" Then
        swModel.ClearSelection2 True
        Dim bRet
        bRet = swFeat.Select2(False, 0)
        If bRet = True Then
            bFound = True
        End If
    End If
    If bFound = False Then
        Set swFeat = swFeat.GetNextFeature()
    End If
Loop

CHINESE COMMANDS GLOSSARY:
- "画圆" or "绘制圆形" or "创建圆" = Draw a circle
- "画矩形" or "绘制矩形" or "创建矩形" = Draw a rectangle
- "画线" or "绘制直线" or "创建直线" = Draw a line
- "拉伸" or "挤出" = Extrude
- "切除" or "切割" = Cut
- "保存" = Save
"""
        
        messages = [
            ChatMessage("system", system_prompt),
            ChatMessage("user", query)
        ]
        
        response = self.client.chat_completion(messages, temperature=0.2, max_tokens=2000)
        app_logger.log_script_generation(query, response.success)
        
        return response.success, response.content if response.success else response.error
    
    def debug_script(self, script_content: str, error_message: str) -> Tuple[bool, str, str]:
        """调试VBScript脚本"""
        system_prompt = """You are an expert VBScript debugger for SolidWorks automation.
Your task is to analyze VBScript errors and provide fixed code.

When you receive broken VBScript code and error messages, you should:
1. Identify the root cause of the error
2. Provide the corrected VBScript code
3. Explain what was wrong and how you fixed it

Always respond in this format:
FIXED_CODE:
[The corrected VBScript code here]

EXPLANATION:
[Brief explanation of what was wrong and how you fixed it]

Make sure the fixed code:
- Starts with 'Option Explicit'
- Includes proper SolidWorks connection code
- Has proper error handling
- Follows VBScript syntax rules
- Can run independently
"""
        
        user_message = f"""请分析以下VBScript代码中的错误并提供修复后的代码：

错误信息：
{error_message}

有问题的代码：
{script_content}

请提供修复后的完整VBScript代码和解释。"""
        
        messages = [
            ChatMessage("system", system_prompt),
            ChatMessage("user", user_message)
        ]
        
        response = self.client.chat_completion(messages, temperature=0.1, max_tokens=2000)
        
        if response.success:
            content = response.content
            
            # 提取修复后的代码和解释
            fixed_code = ""
            explanation = ""
            
            if "FIXED_CODE:" in content and "EXPLANATION:" in content:
                parts = content.split("EXPLANATION:")
                if len(parts) == 2:
                    code_part = parts[0].replace("FIXED_CODE:", "").strip()
                    explanation = parts[1].strip()
                    
                    # 清理代码部分，移除markdown标记
                    if code_part.startswith("```"):
                        lines = code_part.split('\n')
                        if len(lines) > 1:
                            # 跳过第一行的```vbscript或```
                            code_lines = lines[1:]
                            # 找到最后的```并移除
                            if code_lines and code_lines[-1].strip() == "```":
                                code_lines = code_lines[:-1]
                            fixed_code = '\n'.join(code_lines)
                    else:
                        fixed_code = code_part
            else:
                # 如果格式不正确，将整个响应作为解释
                explanation = content
                fixed_code = script_content  # 保持原代码不变
            
            return True, fixed_code, explanation
        else:
            return False, "", response.error
    
    def provide_guidance(self, user_query: str) -> Tuple[bool, str]:
        """提供用户指导"""
        system_prompt = """你是SolidWorks自动化和VBScript编程方面的专家助手。
你的任务是帮助用户解决SolidWorks脚本开发中遇到的问题。

你应该：
1. 理解用户的问题或需求
2. 提供清晰、准确的解答
3. 如果涉及代码，提供具体的VBScript示例
4. 给出最佳实践建议
5. 用中文回答

你可以帮助用户解决：
- VBScript语法问题
- SolidWorks API使用问题
- 常见错误的解决方案
- 脚本优化建议
- SolidWorks自动化最佳实践
"""
        
        messages = [
            ChatMessage("system", system_prompt),
            ChatMessage("user", user_query)
        ]
        
        response = self.client.chat_completion(messages, temperature=0.3, max_tokens=1500)
        
        return response.success, response.content if response.success else response.error


# 全局API服务实例
api_service = APIService() 