Option Explicit

Dim swApp
Set swApp = GetObject(, "SldWorks.Application")
swApp.Visible = True

Dim swModel
Set swModel = swApp.ActiveDoc
If swModel Is Nothing Then
    WScript.Echo "No active document"
    WScript.Quit(1)
End If

WScript.Echo "Document: " & swModel.GetTitle()

Dim swFeat
Set swFeat = swModel.FirstFeature()

WScript.Echo "Features in the document:"
Do While Not swFeat Is Nothing
    WScript.Echo "- " & swFeat.Name & " (Type: " & swFeat.GetTypeName() & ")"
    Set swFeat = swFeat.GetNextFeature()
Loop

WScript.Echo ""
WScript.Echo "Trying different plane names:"

Dim planeNames(11)
planeNames(0) = "Front Plane"
planeNames(1) = "Top Plane" 
planeNames(2) = "Right Plane"
planeNames(3) = "前视基准面"
planeNames(4) = "上视基准面"
planeNames(5) = "右视基准面"
planeNames(6) = "基准面1"
planeNames(7) = "基准面2"
planeNames(8) = "基准面3"
planeNames(9) = "Plane1"
planeNames(10) = "Plane2"
planeNames(11) = "Plane3"

Dim i
For i = 0 To 11
    Dim bRet
    bRet = swModel.Extension.SelectByID2(planeNames(i), "PLANE", 0, 0, 0, False, 0, Nothing, 0)
    WScript.Echo planeNames(i) & ": " & bRet
    swModel.ClearSelection2 True
Next

WScript.Quit(0)
