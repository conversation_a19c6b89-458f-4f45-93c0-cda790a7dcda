Option Explicit

' 在SolidWorks中绘制圆形的脚本
' 此脚本连接到SolidWorks并绘制一个圆形

' 主要变量
Dim swApp, swModel, swModelDoc, swSketchManager

' 连接到SolidWorks
On Error Resume Next
WScript.Echo "正在连接到SolidWorks..."
Set swApp = GetObject(, "SldWorks.Application")

If Err.Number <> 0 Then
    Err.Clear
    WScript.Echo "SolidWorks未运行。尝试启动SolidWorks..."
    Set swApp = CreateObject("SldWorks.Application")
    
    If Err.Number <> 0 Then
        WScript.Echo "连接SolidWorks时出错: " & Err.Description
        WScript.Quit(1)
    End If
End If

' 显示软件
swApp.Visible = True
WScript.Echo "成功连接到SolidWorks。"
On Error Goto 0

' 创建新文档或使用当前文档
On Error Resume Next
WScript.Echo "正在准备文档..."

' 检查活动文档
Set swModel = swApp.ActiveDoc

If swModel Is Nothing Then
    ' 如果没有打开的文档，创建新文档
    Dim templatePath
    templatePath = swApp.GetUserPreferenceStringValue(0)  ' 零件模板路径
    
    If templatePath = "" Then
        ' 使用默认路径
        templatePath = "C:\ProgramData\SolidWorks\SOLIDWORKS 2023\templates\Part.prtdot"
    End If
    
    WScript.Echo "正在使用模板创建新文档: " & templatePath
    Set swModel = swApp.NewDocument(templatePath, 0, 0, 0)
    
    If Err.Number <> 0 Then
        WScript.Echo "创建新文档时出错: " & Err.Description
        WScript.Quit(1)
    End If
Else
    WScript.Echo "使用现有活动文档..."
End If

' 转换为modelDoc2以访问更多功能
Set swModelDoc = swModel

On Error GoTo 0

' 选择绘图平面或曲面
On Error Resume Next
swModelDoc.ClearSelection2 True
Dim boolStatus
boolStatus = swModelDoc.Extension.SelectByID2("Front Plane", "PLANE", 0, 0, 0, False, 0, Nothing, 0)

If boolStatus = False Then
    ' 尝试用中文名称选择
    boolStatus = swModelDoc.Extension.SelectByID2("前视基准面", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
    
    If boolStatus = False Then
        ' 使用上视基准面作为第二选择
        boolStatus = swModelDoc.Extension.SelectByID2("Top Plane", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
        
        If boolStatus = False Then
            ' 尝试用中文名称选择
            boolStatus = swModelDoc.Extension.SelectByID2("上视基准面", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
            
            If boolStatus = False Then
                WScript.Echo "错误: 找不到合适的绘图平面。"
                WScript.Quit(1)
            End If
        End If
    End If
End If

On Error GoTo 0

' 创建新草图
Set swSketchManager = swModelDoc.SketchManager
swSketchManager.InsertSketch True
WScript.Echo "新草图已创建。"

' 绘制圆形
Dim centerX, centerY, radius
centerX = 0        ' 圆心，X坐标
centerY = 0        ' 圆心，Y坐标
radius = 0.05      ' 圆半径（米）

WScript.Echo "正在绘制半径为 " & (radius * 1000) & " 毫米的圆形..."
swSketchManager.CreateCircleByRadius centerX, centerY, 0, radius

' 结束草图
swSketchManager.InsertSketch True

' 显示等轴测视图并适配视图
swModelDoc.ShowNamedView2 "*Isometric", 7
swModelDoc.ViewZoomtofit2

WScript.Echo "SUCCESS: 圆形绘制成功。"
WScript.Quit(0) 