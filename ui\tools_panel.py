#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具面板组件
提供快捷建模工具和便捷操作
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Callable, Dict, List
import os

from config import config_manager
from logger import app_logger
from .components import CustomButton


class ToolsPanel(tk.Frame):
    """工具面板 - 提供快捷建模工具和便捷操作"""
    
    def __init__(self, parent, tool_callback: Optional[Callable] = None, **kwargs):
        """
        Args:
            parent: 父窗口
            tool_callback: 工具执行回调函数
        """
        self.ui_config = config_manager.config.ui
        self.tool_callback = tool_callback
        
        super().__init__(parent, 
                        bg=self.ui_config.card_color,
                        **kwargs)
        
        # 工具分类数据
        self.tool_categories = {
            "基础几何体": {
                "立方体": {
                    "description": "创建立方体/长方体",
                    "icon": "📦",
                    "script": "create_box"
                },
                "圆柱体": {
                    "description": "创建圆柱体",
                    "icon": "🥫", 
                    "script": "create_cylinder"
                },
                "球体": {
                    "description": "创建球体",
                    "icon": "⚽",
                    "script": "create_sphere"
                },
                "圆锥体": {
                    "description": "创建圆锥体",
                    "icon": "🔺",
                    "script": "create_cone"
                }
            },
            "机械零件": {
                "直齿圆柱齿轮": {
                    "description": "创建标准直齿圆柱齿轮",
                    "icon": "⚙️",
                    "script": "create_spur_gear"
                },
                "螺栓": {
                    "description": "创建标准螺栓",
                    "icon": "🔩",
                    "script": "create_bolt"
                },
                "螺母": {
                    "description": "创建标准螺母", 
                    "icon": "🔧",
                    "script": "create_nut"
                },
                "弹簧": {
                    "description": "创建螺旋弹簧",
                    "icon": "🌀",
                    "script": "create_spring"
                }
            },
            "便捷工具": {
                "测量工具": {
                    "description": "快速测量距离、角度、面积",
                    "icon": "📏",
                    "script": "measurement_tool"
                },
                "材料设置": {
                    "description": "快速设置常用材料属性",
                    "icon": "🧱",
                    "script": "material_tool"
                },
                "视图工具": {
                    "description": "快速切换标准视图",
                    "icon": "👁️",
                    "script": "view_tool"
                },
                "导出工具": {
                    "description": "批量导出为常用格式",
                    "icon": "📤",
                    "script": "export_tool"
                }
            }
        }
        
        self._create_ui()
    
    def _create_ui(self):
        """创建用户界面"""
        # 标题
        title_label = tk.Label(
            self,
            text="🛠️ 快捷工具",
            font=("Microsoft YaHei", 14, "bold"),
            bg=self.ui_config.card_color,
            fg=self.ui_config.text_color
        )
        title_label.pack(pady=(10, 15))
        
        # 创建笔记本控件用于分类
        self.notebook = ttk.Notebook(self)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        # 为每个分类创建选项卡
        for category_name, tools in self.tool_categories.items():
            self._create_category_tab(category_name, tools)
        
        # 配置笔记本样式
        self._configure_notebook_style()
    
    def _create_category_tab(self, category_name: str, tools: Dict):
        """创建分类选项卡"""
        # 创建选项卡框架
        tab_frame = tk.Frame(self.notebook, bg=self.ui_config.input_bg)
        self.notebook.add(tab_frame, text=category_name)
        
        # 创建滚动区域
        canvas = tk.Canvas(tab_frame, bg=self.ui_config.input_bg, highlightthickness=0)
        scrollbar = ttk.Scrollbar(tab_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.ui_config.input_bg)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 布局滚动组件
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 创建工具按钮网格
        row = 0
        col = 0
        max_cols = 2  # 每行最多2个工具
        
        for tool_name, tool_info in tools.items():
            self._create_tool_button(
                scrollable_frame, 
                tool_name, 
                tool_info,
                row, 
                col
            )
            
            col += 1
            if col >= max_cols:
                col = 0
                row += 1
    
    def _create_tool_button(self, parent, tool_name: str, tool_info: Dict, row: int, col: int):
        """创建工具按钮"""
        # 工具按钮框架
        tool_frame = tk.Frame(
            parent,
            bg=self.ui_config.card_color,
            relief=tk.RAISED,
            bd=1
        )
        tool_frame.grid(row=row, column=col, padx=5, pady=5, sticky="ew")
        
        # 配置列权重
        parent.grid_columnconfigure(col, weight=1)
        
        # 图标和名称
        icon_label = tk.Label(
            tool_frame,
            text=tool_info["icon"],
            font=("Microsoft YaHei", 20),
            bg=self.ui_config.card_color,
            fg=self.ui_config.text_color
        )
        icon_label.pack(pady=(10, 5))
        
        name_label = tk.Label(
            tool_frame,
            text=tool_name,
            font=("Microsoft YaHei", 10, "bold"),
            bg=self.ui_config.card_color,
            fg=self.ui_config.text_color
        )
        name_label.pack()
        
        # 描述
        desc_label = tk.Label(
            tool_frame,
            text=tool_info["description"],
            font=("Microsoft YaHei", 8),
            bg=self.ui_config.card_color,
            fg=self.ui_config.secondary_text,
            wraplength=120
        )
        desc_label.pack(pady=(2, 5))
        
        # 使用按钮
        use_btn = CustomButton(
            tool_frame,
            "使用",
            lambda t=tool_info["script"], n=tool_name: self._execute_tool(t, n),
            style="primary",
            width=8
        )
        use_btn.pack(pady=(0, 10))
        
        # 鼠标悬停效果
        def on_enter(event):
            tool_frame.config(bg=self.ui_config.highlight_color)
            icon_label.config(bg=self.ui_config.highlight_color)
            name_label.config(bg=self.ui_config.highlight_color)
            desc_label.config(bg=self.ui_config.highlight_color)
        
        def on_leave(event):
            tool_frame.config(bg=self.ui_config.card_color)
            icon_label.config(bg=self.ui_config.card_color)
            name_label.config(bg=self.ui_config.card_color)
            desc_label.config(bg=self.ui_config.card_color)
        
        tool_frame.bind("<Enter>", on_enter)
        tool_frame.bind("<Leave>", on_leave)
        icon_label.bind("<Enter>", on_enter)
        icon_label.bind("<Leave>", on_leave)
        name_label.bind("<Enter>", on_enter)
        name_label.bind("<Leave>", on_leave)
        desc_label.bind("<Enter>", on_enter)
        desc_label.bind("<Leave>", on_leave)
    
    def _configure_notebook_style(self):
        """配置笔记本样式"""
        style = ttk.Style()
        
        # 配置选项卡样式
        style.theme_use('clam')
        style.configure(
            'TNotebook',
            background=self.ui_config.card_color,
            borderwidth=0
        )
        style.configure(
            'TNotebook.Tab',
            background=self.ui_config.sidebar_color,
            foreground=self.ui_config.text_color,
            padding=[12, 8],
            font=("Microsoft YaHei", 9)
        )
        style.map(
            'TNotebook.Tab',
            background=[('selected', self.ui_config.accent_color)],
            foreground=[('selected', 'white')]
        )
    
    def _execute_tool(self, script_name: str, tool_name: str):
        """执行工具"""
        try:
            if self.tool_callback:
                self.tool_callback(script_name, tool_name)
            else:
                messagebox.showinfo("工具执行", f"执行工具: {tool_name}")
                app_logger.main_logger.info(f"工具执行: {tool_name} ({script_name})")
        except Exception as e:
            app_logger.log_error(e, f"执行工具 {tool_name}")
            messagebox.showerror("工具执行失败", f"执行工具 {tool_name} 时出错:\n{str(e)}")
