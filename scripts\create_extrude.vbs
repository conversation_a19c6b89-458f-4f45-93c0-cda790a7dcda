Option Explicit

' 在SolidWorks中创建草图并拉伸的脚本
' 此脚本独立运行，不需要任何外部文件

' 主要变量
Dim swApp, swModel, swModelDoc, swSketchManager, swFeatureManager

' 连接到SolidWorks
On Error Resume Next
WScript.Echo "正在连接到SolidWorks..."
Set swApp = GetObject(, "SldWorks.Application")

If Err.Number <> 0 Then
    Err.Clear
    WScript.Echo "SolidWorks未运行。尝试启动SolidWorks..."
    Set swApp = CreateObject("SldWorks.Application")
    
    If Err.Number <> 0 Then
        WScript.Echo "连接SolidWorks时出错: " & Err.Description
        WScript.Quit(1)
    End If
End If

' 显示软件
swApp.Visible = True
WScript.Echo "成功连接到SolidWorks。"
On Error Goto 0

' 创建新零件文档
On Error Resume Next
WScript.Echo "正在创建新零件文档..."

' 使用NewDocument函数创建新零件文档
Dim docType
docType = 1  ' 类型1为零件

' 零件模板路径
Dim templatePath
templatePath = swApp.GetUserPreferenceStringValue(0)  ' 零件模板路径

If templatePath = "" Then
    ' 如果找不到模板路径，使用默认路径
    templatePath = "C:\ProgramData\SolidWorks\SOLIDWORKS 2023\templates\Part.prtdot"
End If

WScript.Echo "使用零件模板: " & templatePath

Set swModel = swApp.NewDocument(templatePath, 0, 0, 0)

If Err.Number <> 0 Then
    WScript.Echo "创建零件文档时出错: " & Err.Description
    WScript.Quit(1)
End If

If swModel Is Nothing Then
    WScript.Echo "错误: 零件模型未创建。"
    WScript.Quit(1)
End If

' 转换为modelDoc2以访问更多功能
Set swModelDoc = swModel

' 检查文档类型
If swModelDoc.GetType <> 1 Then
    WScript.Echo "错误: 创建的文档不是零件类型！"
    WScript.Quit(1)
End If

WScript.Echo "新零件文档创建成功。"

' 获取草图管理器和特征管理器
Set swSketchManager = swModelDoc.SketchManager
Set swFeatureManager = swModelDoc.FeatureManager

' 选择前视基准面(Front Plane)
On Error Resume Next
swModelDoc.Extension.SelectByID2 "Front Plane", "PLANE", 0, 0, 0, False, 0, Nothing, 0
If Err.Number <> 0 Then
    WScript.Echo "尝试用中文名称选择Front Plane..."
    swModelDoc.Extension.SelectByID2 "前视基准面", "PLANE", 0, 0, 0, False, 0, Nothing, 0
    If Err.Number <> 0 Then
        WScript.Echo "选择基准面时出错: " & Err.Description
        WScript.Quit(1)
    End If
End If
On Error Goto 0

' 在选定基准面上创建草图
WScript.Echo "正在创建草图..."
swSketchManager.InsertSketch True

' 绘制一个圆
Dim circleRadius
circleRadius = 0.025 ' 25毫米转换为米
swSketchManager.CreateCircleByRadius 0, 0, 0, circleRadius
WScript.Echo "创建了半径为25毫米的圆形。"

' 结束草图
swSketchManager.InsertSketch True

' 拉伸草图
WScript.Echo "正在拉伸草图..."

' 选择最后创建的草图
On Error Resume Next
Dim lastSketch
Set lastSketch = swModelDoc.GetActiveSketch2()
If lastSketch Is Nothing Then
    WScript.Echo "按名称选择草图..."
    swModelDoc.ClearSelection2 True
    swModelDoc.Extension.SelectByID2 "Sketch1", "SKETCH", 0, 0, 0, False, 0, Nothing, 0
End If

' 拉伸
Dim myFeature
Dim boolStatus
boolStatus = swModelDoc.Extension.SelectByID2("Sketch1", "SKETCH", 0, 0, 0, False, 0, Nothing, 0)
WScript.Echo "草图选择: " & (boolStatus)

' 拉伸
Dim extrudeDepth
extrudeDepth = 0.01 ' 10毫米

' 使用简单设置创建拉伸特征
Set myFeature = swFeatureManager.FeatureExtrusion2(True, False, True, 0, 0, extrudeDepth, 0.01, False, 0, False, False, 0, 0, False, False, False, False, False, False, False, False, False, False)

If Err.Number <> 0 Then
    WScript.Echo "拉伸时出错: " & Err.Description
    Err.Clear
End If

If myFeature Is Nothing Then
    WScript.Echo "错误: 拉伸特征未创建。尝试其他方法..."
    
    ' 使用常规方法尝试(菜单)
    swModelDoc.ClearSelection2 True
    swModelDoc.Extension.SelectByID2 "Sketch1", "SKETCH", 0, 0, 0, False, 0, Nothing, 0
    
    ' 使用较少设置再次尝试
    Set myFeature = swFeatureManager.FeatureExtrusion2(True, False, False, 0, 0, extrudeDepth, 0.01, False, 0, False, False, 0, 0, False, False, False, False, False, False, False, False, False, False)
    
    If myFeature Is Nothing Then
        WScript.Echo "错误: 拉伸特征未创建。第二次尝试也失败了。"
        
        ' 尝试使用菜单添加拉伸
        Dim sModelName
        sModelName = swModelDoc.GetPathName
        If Len(sModelName) = 0 Then
            sModelName = "Untitled"
        End If
        
        WScript.Echo "使用菜单进行拉伸..."
        swApp.RunCommand swCommands_Insert_Boss_Extrude, ""
    Else
        WScript.Echo "使用第二种方法拉伸成功。"
    End If
Else
    WScript.Echo "拉伸成功。"
End If

On Error GoTo 0

' 显示等轴测视图
swModelDoc.ShowNamedView2 "*Isometric", 7
swModelDoc.ViewZoomtofit2

WScript.Echo "SUCCESS: 草图和拉伸创建成功(或已尝试)"
WScript.Quit(0) 