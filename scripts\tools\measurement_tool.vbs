Option Explicit

' SolidWorks测量工具脚本
' 提供快速测量功能
' 作者: SolidWorks AI助手

' 主要变量
Dim swApp, swModel, swModelDoc

' 连接到SolidWorks
On Error Resume Next
WScript.Echo "正在连接到SolidWorks..."
Set swApp = GetObject(, "SldWorks.Application")

If Err.Number <> 0 Then
    Err.Clear
    WScript.Echo "SolidWorks未运行，尝试启动..."
    Set swApp = CreateObject("SldWorks.Application")
    If Err.Number <> 0 Then
        WScript.Echo "连接SolidWorks时出错: " & Err.Description
        WScript.Quit(1)
    End If
End If

swApp.Visible = True
WScript.Echo "成功连接到SolidWorks。"
On Error Goto 0

' 获取当前活动文档
Set swModel = swApp.ActiveDoc
If swModel Is Nothing Then
    WScript.Echo "没有打开的文档。请先打开一个SolidWorks文档。"
    WScript.Quit(1)
End If

Set swModelDoc = swModel

' 显示测量工具选择菜单
Dim choice
choice = InputBox("请选择测量类型:" & vbCrLf & _
                 "1 - 距离测量" & vbCrLf & _
                 "2 - 角度测量" & vbCrLf & _
                 "3 - 面积测量" & vbCrLf & _
                 "4 - 体积测量" & vbCrLf & _
                 "5 - 质量属性", "测量工具", "1")

If choice = "" Then WScript.Quit(0)

Select Case CInt(choice)
    Case 1
        ' 距离测量
        WScript.Echo "启动距离测量工具..."
        WScript.Echo "请在SolidWorks中选择两个点、边或面进行距离测量。"
        swApp.RunCommand 1557, ""  ' 测量工具
        
    Case 2
        ' 角度测量
        WScript.Echo "启动角度测量工具..."
        WScript.Echo "请在SolidWorks中选择两条边或两个面进行角度测量。"
        swApp.RunCommand 1557, ""  ' 测量工具
        
    Case 3
        ' 面积测量
        WScript.Echo "启动面积测量..."
        WScript.Echo "请选择要测量面积的面。"
        
        ' 获取选择的面
        Dim selMgr
        Set selMgr = swModelDoc.SelectionManager
        
        If selMgr.GetSelectedObjectCount2(-1) > 0 Then
            Dim selObj
            Set selObj = selMgr.GetSelectedObject6(1, -1)
            
            If Not selObj Is Nothing Then
                Dim face
                Set face = selObj
                
                If Not face Is Nothing Then
                    Dim area
                    area = face.GetArea()
                    WScript.Echo "面积: " & FormatNumber(area * 1000000, 2) & " mm²"
                End If
            End If
        Else
            WScript.Echo "请先选择一个面，然后重新运行此脚本。"
        End If
        
    Case 4
        ' 体积测量
        WScript.Echo "计算模型体积..."
        
        Dim massProp
        Set massProp = swModelDoc.Extension.CreateMassProperty()
        
        If Not massProp Is Nothing Then
            Dim volume
            volume = massProp.Volume
            WScript.Echo "体积: " & FormatNumber(volume * 1000000000, 2) & " mm³"
            WScript.Echo "体积: " & FormatNumber(volume * 1000, 6) & " 升"
        Else
            WScript.Echo "无法计算体积。请确保模型是实体。"
        End If
        
    Case 5
        ' 质量属性
        WScript.Echo "计算质量属性..."
        
        Set massProp = swModelDoc.Extension.CreateMassProperty()
        
        If Not massProp Is Nothing Then
            Dim mass, volume, density
            mass = massProp.Mass
            volume = massProp.Volume
            density = massProp.Density
            
            WScript.Echo "=== 质量属性 ==="
            WScript.Echo "体积: " & FormatNumber(volume * 1000000000, 2) & " mm³"
            WScript.Echo "密度: " & FormatNumber(density, 6) & " kg/m³"
            WScript.Echo "质量: " & FormatNumber(mass, 6) & " kg"
            
            ' 重心坐标
            Dim cog
            cog = massProp.CenterOfMass
            WScript.Echo "重心坐标 (mm):"
            WScript.Echo "  X: " & FormatNumber(cog(0) * 1000, 2)
            WScript.Echo "  Y: " & FormatNumber(cog(1) * 1000, 2)
            WScript.Echo "  Z: " & FormatNumber(cog(2) * 1000, 2)
            
            ' 显示质量属性对话框
            swApp.RunCommand 1556, ""  ' 质量属性
        Else
            WScript.Echo "无法计算质量属性。"
        End If
        
    Case Else
        WScript.Echo "无效的选择。"
        WScript.Quit(1)
End Select

WScript.Echo "SUCCESS: 测量工具执行完成。"
WScript.Quit(0)
