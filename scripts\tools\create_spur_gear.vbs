Option Explicit

' 创建直齿圆柱齿轮的SolidWorks脚本
' 此脚本创建一个简化的直齿圆柱齿轮
' 作者: SolidWorks AI助手

' 主要变量
Dim swApp, swModel, swModelDoc, swSketchManager, swFeatureManager
Dim teethCount, module, thickness, pitchRadius, outerRadius, innerRadius

' 获取用户输入的齿轮参数
teethCount = InputBox("请输入齿数:", "齿轮参数", "20")
If teethCount = "" Then WScript.Quit(0)
teethCount = CInt(teethCount)

module = InputBox("请输入模数 (mm):", "齿轮参数", "2")
If module = "" Then WScript.Quit(0)
module = CDbl(module)

thickness = InputBox("请输入齿轮厚度 (mm):", "齿轮参数", "10")
If thickness = "" Then WScript.Quit(0)
thickness = CDbl(thickness) / 1000  ' 转换为米

' 计算齿轮基本尺寸
pitchRadius = (teethCount * module) / 2000  ' 分度圆半径，转换为米
outerRadius = pitchRadius + (module / 1000)  ' 齿顶圆半径
innerRadius = pitchRadius - (1.25 * module / 1000)  ' 齿根圆半径

' 连接到SolidWorks
On Error Resume Next
WScript.Echo "正在连接到SolidWorks..."
Set swApp = GetObject(, "SldWorks.Application")

If Err.Number <> 0 Then
    Err.Clear
    WScript.Echo "SolidWorks未运行，尝试启动..."
    Set swApp = CreateObject("SldWorks.Application")
    If Err.Number <> 0 Then
        WScript.Echo "连接SolidWorks时出错: " & Err.Description
        WScript.Quit(1)
    End If
End If

swApp.Visible = True
WScript.Echo "成功连接到SolidWorks。"
On Error Goto 0

' 创建新零件文档
On Error Resume Next
WScript.Echo "正在创建新零件文档..."

Set swModel = swApp.NewPart()
If swModel Is Nothing Then
    Set swModel = swApp.NewDocument("", 1, 0, 0)
End If

If swModel Is Nothing Then
    WScript.Echo "错误: 零件模型未创建。"
    WScript.Quit(1)
End If

Set swModelDoc = swModel
Set swSketchManager = swModelDoc.SketchManager
Set swFeatureManager = swModelDoc.FeatureManager

WScript.Echo "新零件文档创建成功。"
On Error Goto 0

' 选择前视基准面
On Error Resume Next
Dim boolStatus
boolStatus = swModelDoc.Extension.SelectByID2("Front Plane", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
If Not boolStatus Then
    boolStatus = swModelDoc.Extension.SelectByID2("前视基准面", "PLANE", 0, 0, 0, False, 0, Nothing, 0)
End If

If Not boolStatus Then
    WScript.Echo "选择基准面失败"
    WScript.Quit(1)
End If
On Error Goto 0

' 创建齿轮基本轮廓
WScript.Echo "正在创建齿轮轮廓..."
swSketchManager.InsertSketch True

' 绘制齿轮的基本圆形轮廓（简化版本）
swSketchManager.CreateCircleByRadius 0, 0, 0, outerRadius
swSketchManager.CreateCircleByRadius 0, 0, 0, innerRadius

' 创建中心孔
Dim holeRadius
holeRadius = pitchRadius * 0.2  ' 中心孔半径为分度圆半径的20%
swSketchManager.CreateCircleByRadius 0, 0, 0, holeRadius

WScript.Echo "创建了齿轮基本轮廓。"
WScript.Echo "齿数: " & teethCount & ", 模数: " & module & " mm"
WScript.Echo "分度圆直径: " & (pitchRadius * 2000) & " mm"

' 结束草图
swSketchManager.InsertSketch True

' 拉伸草图创建齿轮
WScript.Echo "正在拉伸草图..."

' 选择外圆
swModelDoc.ClearSelection2 True
boolStatus = swModelDoc.Extension.SelectByID2("Arc1", "SKETCHSEGMENT", 0, 0, 0, False, 0, Nothing, 0)

If Not boolStatus Then
    ' 如果选择失败，选择整个草图
    boolStatus = swModelDoc.Extension.SelectByID2("Sketch1", "SKETCH", 0, 0, 0, False, 0, Nothing, 0)
End If

If boolStatus Then
    ' 创建拉伸特征
    Dim myFeature
    Set myFeature = swFeatureManager.FeatureExtrusion2(True, False, False, 0, 0, thickness, 0.01, False, 0, False, False, 0, 0, False, False, False, False, False, False, False, False, False, False)
    
    If myFeature Is Nothing Then
        WScript.Echo "拉伸失败，尝试其他方法..."
        swApp.RunCommand 392, ""  ' Insert Boss/Base Extrude
    Else
        WScript.Echo "拉伸成功。"
    End If
Else
    WScript.Echo "选择草图失败"
End If

' 显示等轴测视图并缩放到合适大小
swModelDoc.ShowNamedView2 "*Isometric", 7
swModelDoc.ViewZoomtofit2

' 重命名特征
On Error Resume Next
swModelDoc.Extension.SelectByID2 "Boss-Extrude1", "BODYFEATURE", 0, 0, 0, False, 0, Nothing, 0
swModelDoc.SelectedFeatureProperties 0, 0, 0, 0, 0, 0, 0, True, False, "齿轮基体"
On Error Goto 0

WScript.Echo "SUCCESS: 简化齿轮创建完成！"
WScript.Echo "注意: 这是一个简化的齿轮模型，实际齿形需要专业的齿轮设计工具。"
WScript.Echo "参数: " & teethCount & "齿, 模数" & module & "mm, 厚度" & (thickness*1000) & "mm"
WScript.Quit(0)
